"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  FlaskConical,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Monitor,
  Smartphone,
  Tablet
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Deal } from "@/lib/types/deal"
import { ScoreTab } from "./score-tab"
import { testDeals } from "./test-data"
import { DealCard } from "../deal-card"

interface TestValidationProps {
  className?: string
}

const deviceSizes = [
  { name: "Mobile", icon: Smartphone, width: "375px", height: "667px" },
  { name: "Tablet", icon: Tablet, width: "768px", height: "1024px" },
  { name: "Des<PERSON>op", icon: Monitor, width: "1440px", height: "900px" }
]

const testCases = [
  {
    name: "Strong Match with Bonus",
    description: "Deal with high thesis match and bonus points",
    deal: testDeals[0],
    expectedBehavior: [
      "Shows 95% thesis match badge",
      "Displays bonus indicators (+15 pts)",
      "Green color coding for strong match",
      "Detailed question breakdown with instances",
      "Bonus/penalty section with explanations"
    ]
  },
  {
    name: "Excluded Deal",
    description: "Deal excluded by filter",
    deal: testDeals[1],
    expectedBehavior: [
      "Shows exclusion status prominently",
      "Hides thesis match percentage",
      "Displays exclusion reason",
      "Red color coding for exclusion",
      "Scoring data hidden due to exclusion"
    ]
  },
  {
    name: "No Scoring Data",
    description: "Deal without scoring analysis",
    deal: testDeals[2],
    expectedBehavior: [
      "Shows empty state with clear message",
      "Provides action to check analysis status",
      "No scoring badges or percentages",
      "Clean, informative placeholder"
    ]
  },
  {
    name: "Deal with Penalties",
    description: "Deal with penalty deductions",
    deal: testDeals[3],
    expectedBehavior: [
      "Shows 55% thesis match (after penalties)",
      "Displays penalty indicators (-20 pts)",
      "Yellow/orange color coding for moderate match",
      "Penalty breakdown with explanations",
      "Net impact calculation visible"
    ]
  }
]

export function TestValidation({ className }: TestValidationProps) {
  const [selectedDevice, setSelectedDevice] = useState(0)
  const [selectedTest, setSelectedTest] = useState(0)
  const [validationResults, setValidationResults] = useState<Record<string, boolean>>({})

  const currentTest = testCases[selectedTest]
  const currentDevice = deviceSizes[selectedDevice]

  const validateTestCase = (testIndex: number, passed: boolean) => {
    setValidationResults(prev => ({
      ...prev,
      [`test-${testIndex}`]: passed
    }))
  }

  const allTestsPassed = testCases.every((_, index) => validationResults[`test-${index}`])

  return (
    <div className={cn("space-y-8", className)}>
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-3">
          <FlaskConical className="h-8 w-8 text-blue-600" />
          <h1 className="text-3xl font-bold">Deal Scoring UI Validation</h1>
        </div>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Comprehensive testing suite for the enhanced deal scoring interface. 
          Test all edge cases and responsive behavior across devices.
        </p>
        
        {/* Overall Status */}
        <div className="flex items-center justify-center gap-4">
          <Badge 
            className={cn(
              "px-4 py-2 text-sm font-semibold",
              allTestsPassed 
                ? "bg-green-100 text-green-800 border-green-200"
                : "bg-yellow-100 text-yellow-800 border-yellow-200"
            )}
          >
            {allTestsPassed ? (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                All Tests Passed
              </>
            ) : (
              <>
                <AlertTriangle className="h-4 w-4 mr-2" />
                Testing in Progress
              </>
            )}
          </Badge>
          <span className="text-sm text-muted-foreground">
            {Object.values(validationResults).filter(Boolean).length} / {testCases.length} tests passed
          </span>
        </div>
      </div>

      {/* Device Size Selector */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Monitor className="h-5 w-5" />
            Device Testing
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 flex-wrap">
            {deviceSizes.map((device, index) => {
              const Icon = device.icon
              return (
                <Button
                  key={device.name}
                  variant={selectedDevice === index ? "default" : "outline"}
                  onClick={() => setSelectedDevice(index)}
                  className="gap-2"
                >
                  <Icon className="h-4 w-4" />
                  {device.name}
                  <span className="text-xs opacity-70">
                    {device.width} × {device.height}
                  </span>
                </Button>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Test Cases */}
      <div className="grid gap-8 lg:grid-cols-2">
        {/* Test Case Selector */}
        <Card>
          <CardHeader>
            <CardTitle>Test Cases</CardTitle>
            <p className="text-sm text-muted-foreground">
              Select a test case to validate the UI behavior
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            {testCases.map((testCase, index) => (
              <div
                key={index}
                className={cn(
                  "p-4 rounded-lg border cursor-pointer transition-all",
                  selectedTest === index 
                    ? "border-blue-200 bg-blue-50" 
                    : "border-gray-200 hover:border-gray-300"
                )}
                onClick={() => setSelectedTest(index)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-semibold">{testCase.name}</h4>
                    <p className="text-sm text-muted-foreground mt-1">
                      {testCase.description}
                    </p>
                    
                    <div className="mt-3 space-y-1">
                      <p className="text-xs font-medium text-gray-700">Expected Behavior:</p>
                      <ul className="text-xs text-gray-600 space-y-1">
                        {testCase.expectedBehavior.map((behavior, idx) => (
                          <li key={idx} className="flex items-start gap-2">
                            <span className="text-blue-500 mt-0.5">•</span>
                            {behavior}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 ml-4">
                    {validationResults[`test-${index}`] !== undefined && (
                      <Badge
                        className={cn(
                          "text-xs",
                          validationResults[`test-${index}`]
                            ? "bg-green-100 text-green-800 border-green-200"
                            : "bg-red-100 text-red-800 border-red-200"
                        )}
                      >
                        {validationResults[`test-${index}`] ? (
                          <CheckCircle className="h-3 w-3 mr-1" />
                        ) : (
                          <XCircle className="h-3 w-3 mr-1" />
                        )}
                        {validationResults[`test-${index}`] ? "Pass" : "Fail"}
                      </Badge>
                    )}
                    
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={(e) => {
                          e.stopPropagation()
                          validateTestCase(index, true)
                        }}
                        className="h-8 px-3 text-xs"
                      >
                        ✓ Pass
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={(e) => {
                          e.stopPropagation()
                          validateTestCase(index, false)
                        }}
                        className="h-8 px-3 text-xs"
                      >
                        ✗ Fail
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Preview Area */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Live Preview</span>
              <Badge variant="outline" className="text-xs">
                {currentDevice.name} - {currentDevice.width} × {currentDevice.height}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg overflow-hidden bg-gray-50">
              <div 
                className="bg-white overflow-auto"
                style={{
                  width: currentDevice.width,
                  height: currentDevice.height,
                  maxWidth: "100%",
                  maxHeight: "600px"
                }}
              >
                <div className="p-4 space-y-6">
                  {/* Deal Card Preview */}
                  <div>
                    <h4 className="text-sm font-medium mb-3">Deal Card:</h4>
                    <DealCard deal={currentTest.deal} index={0} />
                  </div>
                  
                  {/* Score Tab Preview */}
                  <div>
                    <h4 className="text-sm font-medium mb-3">Score Tab:</h4>
                    <ScoreTab deal={currentTest.deal} />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
