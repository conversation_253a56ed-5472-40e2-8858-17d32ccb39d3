"""
Research queue handler for external signals generation.
"""

import json
from typing import Any, Dict, Optional

from app.core.logging import get_logger
from app.models.queue import Job
from app.services.factory import get_research_service
from app.services.queue.handlers.base import BaseJobHandler
from app.services.research.interface import IResearchService
from app.utils.aws.s3 import S3Storage

logger = get_logger(__name__)


class ResearchHandler(BaseJobHandler):
    """Handler for research queue jobs."""

    def __init__(self):
        """Initialize the research handler."""
        super().__init__()
        self.research_service: Optional[IResearchService] = None
        self.s3_storage: Optional[S3Storage] = None

    async def _initialize_services(self) -> None:
        """Initialize handler services."""
        self.logger.debug("Initializing services for ResearchHandler")

        # Initialize research service
        self.research_service = await get_research_service()  # type: ignore
        if self.research_service:
            await self.research_service.initialize()

        # Initialize S3 storage for context block loading
        self.s3_storage = S3Storage()
        if self.s3_storage:
            await self.s3_storage.initialize()

        self.logger.debug("Services initialized successfully")

    async def cleanup(self) -> None:
        """Cleanup handler resources."""
        if self.research_service:
            await self.research_service.cleanup()
        if self.s3_storage:
            await self.s3_storage.cleanup()

    async def process(self, job: Job) -> Optional[Dict[str, Any]]:
        """
        Process a research job.

        Args:
            job: The research job to process

        Returns:
            Processing result
        """
        payload = job.payload
        self.logger.info(f"Processing research job: {job.id}")

        try:
            # Extract job parameters
            deal_id = payload.get("deal_id")
            org_id = payload.get("org_id")
            context_block_url = payload.get("context_block_url")
            force_refresh = payload.get("force_refresh", False)

            if not all([deal_id, org_id, context_block_url]):
                raise ValueError(
                    "Missing required parameters: deal_id, org_id, or context_block_url"
                )

            self.logger.info(f"Generating external signals for deal {deal_id}")

            # Load context block from S3
            if not context_block_url:
                raise ValueError("context_block_url is required")

            context_block = await self._load_context_block(context_block_url)
            if not context_block:
                raise ValueError(
                    f"Failed to load context block from {context_block_url}"
                )

            # Generate external signals research
            if not self.research_service:
                raise RuntimeError("Research service not initialized")

            external_signals = await self.research_service.generate_external_signals(
                deal_id=str(deal_id),
                context_block=context_block,
                force_refresh=force_refresh,
            )

            self.logger.info(
                f"Successfully generated external signals for deal {deal_id}"
            )

            return {
                "success": True,
                "deal_id": deal_id,
                "research_generated": True,
                "components": {
                    "competitors": external_signals.competitors is not None,
                    "market": external_signals.market is not None,
                    "news": external_signals.news is not None,
                    "summary": external_signals.summary is not None,
                },
                "generated_at": external_signals.generated_at,
            }

        except Exception as e:
            self.logger.error(f"Error processing research job {job.id}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "deal_id": payload.get("deal_id"),
            }

    async def _load_context_block(
        self, context_block_url: str
    ) -> Optional[Dict[str, Any]]:
        """Load context block from S3 URL."""
        try:
            if not self.s3_storage:
                raise RuntimeError("S3 storage not initialized")

            # Extract S3 key from URL
            # Expected format: https://bucket.s3.region.amazonaws.com/deals/{deal_id}/context.json
            # or s3://bucket/deals/{deal_id}/context.json
            # if context_block_url.startswith("s3://"):
            #     # Extract key from s3:// URL
            #     parts = context_block_url.replace("s3://", "").split("/", 1)
            #     if len(parts) == 2:
            #         key = parts[1]
            #     else:
            #         raise ValueError(f"Invalid S3 URL format: {context_block_url}")
            # elif "amazonaws.com" in context_block_url:
            #     # Extract key from HTTPS URL
            #     key = context_block_url.split(".amazonaws.com/", 1)[1]
            # else:
            #     # Assume it's already a key
            #     key = context_block_url

            # Load from S3
            data = await self.s3_storage.get_object("/".join(context_block_url.split("//")[1].split('/')[1:]))
            if data:
                return json.loads(data)

            self.logger.error(f'Context block not found at key: {("/".join(context_block_url.split("//")[1].split("/")[1:]))}')
            return None

        except Exception as e:
            self.logger.error(
                f"Error loading context block from {context_block_url}: {str(e)}"
            )
            return None


# Legacy function-based handler for backward compatibility
async def process_research_job(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process a research job.

    Args:
        payload: Job payload containing research data

    Returns:
        Processing result
    """
    handler = ResearchHandler()
    job = Job(id="legacy", type="research_job", payload=payload)

    try:
        await handler.initialize()
        result = await handler.process(job)
        return result or {"success": True}
    except Exception as e:
        logger.error(f"Error processing research job: {str(e)}")
        return {"success": False, "error": str(e)}
    finally:
        await handler.cleanup()


def create_research_handler() -> ResearchHandler:
    """Create a new research handler instance."""
    logger.debug("Creating new ResearchHandler instance")
    return ResearchHandler()


# Register handlers
HANDLERS = {
    "external_signals_research": create_research_handler,
    "research_job": create_research_handler,
}
