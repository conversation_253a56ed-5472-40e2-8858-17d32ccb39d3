// Test data for validating the enhanced deal scoring UI
import { Deal, DealStatus, ExclusionFilterResult, ThesisScoring, ComprehensiveScoring } from "@/lib/types/deal"

// Test case 1: Deal with strong thesis match and bonus points
export const strongMatchDeal: Deal = {
  id: "deal-1",
  org_id: "org-1",
  form_id: "form-1",
  submission_ids: ["sub-1"],
  company_name: "TechCorp AI",
  stage: "Series A",
  sector: ["AI/ML", "Enterprise Software"],
  company_website: "https://techcorp.ai",
  status: DealStatus.REVIEWED,
  exclusion_filter_result: {
    excluded: false,
    filter_name: "Basic Criteria Filter",
    reason: "Passed all basic criteria checks"
  },
  scoring: {
    thesis: {
      total_score: 95,
      normalized_score: 0.95,
      core_score: 85,
      bonus_total: 15,
      penalty_total: 5,
      max_possible_score: 100,
      question_scores: {
        "q1": {
          rule_id: "rule-1",
          question_id: "q1",
          question_type: "boolean",
          question_label: "Do you have a technical co-founder?",
          raw_score: 1.0,
          weight: 10,
          weighted_score: 10,
          explanation: "Yes, <PERSON><PERSON> has 15+ years experience in AI/ML",
          sources: ["submission"],
          ai_generated: false,
          is_repeatable: false
        },
        "q2": {
          rule_id: "rule-2",
          question_id: "q2",
          question_type: "multi_select",
          question_label: "Founder backgrounds",
          raw_score: 0.8,
          weight: 15,
          weighted_score: 12,
          explanation: "2 of 2 founders match target criteria",
          sources: ["submission"],
          ai_generated: true,
          is_repeatable: true,
          instances: [
            {
              score: 1.0,
              matched: true,
              value: "Stanford CS, ex-Google",
              explanation: "Strong technical background"
            },
            {
              score: 0.6,
              matched: true,
              value: "Harvard MBA, ex-McKinsey",
              explanation: "Strong business background"
            }
          ]
        }
      },
      bonus_scores: {
        "bonus-1": {
          rule_id: "bonus-1",
          rule_name: "Female Founder Bonus",
          rule_type: "BONUS",
          points: 10,
          status: "awarded",
          reason: "1 of 2 founders is female",
          explanation: "Diversity bonus for female leadership"
        },
        "bonus-2": {
          rule_id: "bonus-2",
          rule_name: "Y Combinator Alumni",
          rule_type: "BONUS",
          points: 5,
          status: "awarded",
          reason: "Company is YC alumni (W23)",
          explanation: "Accelerator pedigree bonus"
        }
      },
      scoring_details: [],
      thesis_matches: ["thesis-1"],
      best_thesis_id: "thesis-1",
      matching_count: 1
    },
    founders: {
      total_score: 88,
      normalized_score: 0.88,
      ai_analysis: "Strong founding team with complementary skills",
      key_insights: ["Technical expertise", "Business acumen", "Previous exits"]
    },
    market: {
      total_score: 82,
      normalized_score: 0.82,
      ai_analysis: "Large addressable market with strong growth trends",
      key_insights: ["$50B TAM", "20% YoY growth", "Early adoption phase"]
    },
    metadata: {
      scoring_version: "v2.1.0",
      scored_at: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago
      total_rules_processed: 25,
      ai_scoring_used: true
    }
  },
  created_by: "investor-1",
  created_at: Date.now() - 86400000, // 1 day ago
  updated_at: Date.now() - 3600000   // 1 hour ago
}

// Test case 2: Excluded deal
export const excludedDeal: Deal = {
  id: "deal-2",
  org_id: "org-1",
  form_id: "form-1",
  submission_ids: ["sub-2"],
  company_name: "Solo Founder Inc",
  stage: "Pre-Seed",
  sector: ["Consumer"],
  company_website: "https://solofoundr.com",
  status: DealStatus.EXCLUDED,
  exclusion_filter_result: {
    excluded: true,
    filter_id: "filter-solo",
    filter_name: "Solo Founder Exclusion",
    reason: "Company has only one founder, which doesn't meet our investment criteria requiring at least 2 co-founders"
  },
  scoring: {
    thesis: {
      total_score: 65,
      normalized_score: 0.65,
      core_score: 65,
      bonus_total: 0,
      penalty_total: 0,
      max_possible_score: 100,
      question_scores: {
        "q1": {
          rule_id: "rule-1",
          question_id: "q1",
          question_type: "number",
          question_label: "Number of founders",
          raw_score: 0.0,
          weight: 20,
          weighted_score: 0,
          explanation: "Only 1 founder, requirement is 2+",
          sources: ["submission"],
          ai_generated: false,
          is_repeatable: false
        }
      },
      bonus_scores: {},
      scoring_details: [],
      thesis_matches: [],
      matching_count: 0
    },
    founders: {
      total_score: 45,
      normalized_score: 0.45,
      ai_analysis: "Single founder presents execution risk",
      key_insights: ["Solo founder risk", "Limited expertise coverage"]
    },
    market: {
      total_score: 75,
      normalized_score: 0.75,
      ai_analysis: "Good market opportunity but execution concerns",
      key_insights: ["Growing market", "Competitive landscape"]
    },
    metadata: {
      scoring_version: "v2.1.0",
      scored_at: Math.floor(Date.now() / 1000) - 7200, // 2 hours ago
      total_rules_processed: 15,
      ai_scoring_used: true
    }
  },
  created_by: "investor-1",
  created_at: Date.now() - *********, // 2 days ago
  updated_at: Date.now() - 7200000    // 2 hours ago
}

// Test case 3: Deal with no scoring data
export const noScoringDeal: Deal = {
  id: "deal-3",
  org_id: "org-1",
  form_id: "form-1",
  submission_ids: ["sub-3"],
  company_name: "New Startup",
  stage: "Seed",
  sector: ["FinTech"],
  company_website: "https://newstartup.com",
  status: DealStatus.NEW,
  exclusion_filter_result: {
    excluded: false
  },
  // No scoring data
  created_by: "investor-1",
  created_at: Date.now() - 3600000, // 1 hour ago
  updated_at: Date.now() - 1800000  // 30 minutes ago
}

// Test case 4: Deal with penalties
export const penaltyDeal: Deal = {
  id: "deal-4",
  org_id: "org-1",
  form_id: "form-1",
  submission_ids: ["sub-4"],
  company_name: "Risky Ventures",
  stage: "Series B",
  sector: ["Crypto"],
  company_website: "https://riskyventures.io",
  status: DealStatus.TRIAGE,
  exclusion_filter_result: {
    excluded: false
  },
  scoring: {
    thesis: {
      total_score: 55,
      normalized_score: 0.55,
      core_score: 70,
      bonus_total: 5,
      penalty_total: 20,
      max_possible_score: 100,
      question_scores: {
        "q1": {
          rule_id: "rule-1",
          question_id: "q1",
          question_type: "single_select",
          question_label: "Primary business model",
          raw_score: 0.7,
          weight: 15,
          weighted_score: 10.5,
          explanation: "B2B SaaS model with some concerns",
          sources: ["submission"],
          ai_generated: false,
          is_repeatable: false
        }
      },
      bonus_scores: {
        "penalty-1": {
          rule_id: "penalty-1",
          rule_name: "High Risk Sector Penalty",
          rule_type: "PENALTY",
          points: 15,
          status: "awarded",
          reason: "Crypto/Web3 sector carries additional regulatory risk",
          explanation: "Regulatory uncertainty in crypto space"
        },
        "penalty-2": {
          rule_id: "penalty-2",
          rule_name: "Competitive Market Penalty",
          rule_type: "PENALTY",
          points: 5,
          status: "awarded",
          reason: "Highly saturated market with many competitors"
        }
      },
      scoring_details: [],
      thesis_matches: ["thesis-2"],
      matching_count: 1
    },
    founders: {
      total_score: 78,
      normalized_score: 0.78,
      ai_analysis: "Experienced team but in risky sector",
      key_insights: ["Previous crypto experience", "Strong technical skills"]
    },
    market: {
      total_score: 60,
      normalized_score: 0.60,
      ai_analysis: "Large market but regulatory headwinds",
      key_insights: ["Regulatory uncertainty", "Market volatility"]
    },
    metadata: {
      scoring_version: "v2.1.0",
      scored_at: Math.floor(Date.now() / 1000) - 1800, // 30 minutes ago
      total_rules_processed: 20,
      ai_scoring_used: true
    }
  },
  created_by: "investor-1",
  created_at: Date.now() - 259200000, // 3 days ago
  updated_at: Date.now() - 1800000    // 30 minutes ago
}

export const testDeals = [strongMatchDeal, excludedDeal, noScoringDeal, penaltyDeal]
