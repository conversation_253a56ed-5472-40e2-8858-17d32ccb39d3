"use client"

import React, {use<PERSON><PERSON>back, useMemo, useState} from 'react';
import {AnimatePresence, motion} from 'framer-motion';
import {Card, CardContent, CardHeader, CardTitle} from '@/components/ui/card';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {Label} from '@/components/ui/label';
import {Textarea} from '@/components/ui/textarea';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/select';
import {Badge} from '@/components/ui/badge';
import {Separator} from '@/components/ui/separator';
import {Alert, AlertDescription} from '@/components/ui/alert';
import {Info, Plus, Target, Trash2, Save, Loader2, Edit} from 'lucide-react';
import {cn} from '@/lib/utils';
import {FormWithDetails} from '@/lib/types/form';
import {ConditionBuilder} from './condition-builder';
import {MatchRule, FilterCondition, CompoundFilter} from '@/lib/types/thesis';
import {toast} from '@/components/ui/use-toast';

// Use thesis types for consistency
type ConditionItem = FilterCondition | CompoundFilter;

interface MatchingConfigBuilderProps {
  form: FormWithDetails | null;
  matchRules: Partial<MatchRule>[];
  onAddMatchRule: (rule: Partial<MatchRule>) => void;
  onUpdateMatchRule: (index: number, updates: Partial<MatchRule>) => void;
  onRemoveMatchRule: (index: number) => void;
  onSaveMatchRule: (rule: Partial<MatchRule>) => Promise<void>;
  onDeleteMatchRule: (ruleId: string) => Promise<void>;
  className?: string;
}

// Simplified, more reliable component for individual match rule card
const MatchRuleCard = React.memo(({
  rule,
  index,
  isExpanded,
  onToggleExpansion,
  onUpdate,
  onRemove,
  onSave,
  onDelete,
  allQuestions,
  canRemove
}: {
  rule: Partial<MatchRule>;
  index: number;
  isExpanded: boolean;
  onToggleExpansion: () => void;
  onUpdate: (updates: Partial<MatchRule>) => void;
  onRemove: () => void;
  onSave: (rule: Partial<MatchRule>) => Promise<void>;
  onDelete?: (ruleId: string) => Promise<void>;
  allQuestions: any[];
  canRemove: boolean;
}) => {
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  // Simple, reliable dirty state detection - rule is dirty if it has no ID or is in editing mode
  const isDirty = (!rule._id && !rule.id) || isEditing;
  const ruleId = rule._id || rule.id;

  // Generate stable key for this rule
  const ruleKey = useMemo(() => {
    return ruleId || `new-rule-${index}`;
  }, [ruleId, index]);

  // Validate rule before saving
  const isValidRule = useMemo(() => {
    if (!rule.name?.trim()) return false;
    if (!rule.conditions || rule.conditions.length === 0) return false;

    // Check if all conditions have valid values
    const hasValidConditions = rule.conditions.every(condition => {
      if ('question_id' in condition) {
        const question = allQuestions.find(q => (q._id || q.id) === condition.question_id);
        if (!question) return false;
        
        if (question.type === 'number') {
          return condition.question_id &&
                 condition.operator &&
                 (condition.value !== '' && condition.value !== null && condition.value !== undefined);
        } else {
        return condition.question_id &&
               condition.operator &&
               Array.isArray(condition.value) &&
               condition.value.length > 0;
        }
      }
      return true; // Compound conditions are valid if they exist
    });

    return hasValidConditions;
  }, [rule, allQuestions]);

  const handleNameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onUpdate({name: e.target.value});
  }, [onUpdate]);

  const handleDescriptionChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onUpdate({description: e.target.value});
  }, [onUpdate]);

  const handleOperatorChange = useCallback((value: 'and' | 'or') => {
    onUpdate({operator: value});
  }, [onUpdate]);

  const handleConditionsChange = useCallback((conditions: ConditionItem[]) => {
    onUpdate({conditions});
  }, [onUpdate]);

  const handleEdit = useCallback(() => {
    setIsEditing(true);
  }, []);

  const handleCancelEdit = useCallback(() => {
    setIsEditing(false);
    // Note: We're not resetting the form data here as it would require 
    // storing original values. The parent component should handle this if needed.
  }, []);

  const handleSave = useCallback(async () => {
    if (!isValidRule || isSaving) return;

    setIsSaving(true);
    try {
      await onSave(rule);
      setIsEditing(false);
      toast({
        title: "Success",
        description: "Match rule saved successfully.",
      });
    } catch (error) {
      console.error('Failed to save match rule:', error);
      toast({
        title: "Error",
        description: "Failed to save match rule. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  }, [rule, isValidRule, isSaving, onSave]);

  const handleDelete = useCallback(async () => {
    if (!onDelete || !ruleId || isDeleting) return;

    setIsDeleting(true);
    
    try {
      await onDelete(ruleId);
      
      toast({
        title: "Success",
        description: "Match rule deleted successfully.",
      });
    } catch (error) {
      console.error('❌ Failed to delete match rule:', error);
      toast({
        title: "Error",
        description: "Failed to delete match rule. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  }, [ruleId, onDelete, isDeleting]);

  const handleRemove = useCallback(() => {
    onRemove();
  }, [onRemove]);

  return (
    <Card className={cn("border-l-4", isDirty ? "border-l-amber-500" : "border-l-blue-500")}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          {/* Make the whole left header area clickable for expansion */}
          <button
            type="button"
            className={cn(
              "flex items-center gap-3 flex-1 text-left group focus:outline-none",
              isExpanded ? "bg-blue-50/60" : "hover:bg-muted/40",
              "rounded-md transition-colors duration-100 px-2 py-1 cursor-pointer"
            )}
            aria-expanded={isExpanded}
            onClick={onToggleExpansion}
            tabIndex={0}
          >
            {/* Save Button - Show if dirty (new rule or editing) */}
            {isDirty && (
              <Button
                variant="ghost"
                size="sm"
                disabled={!isValidRule || isSaving}
                className="text-green-600 hover:text-green-700 hover:bg-green-50"
                type="button"
                title="Save Rule"
                onClick={e => { e.stopPropagation(); handleSave(); }}
              >
                {isSaving ? (
                  <Loader2 className="size-4 animate-spin" />
                ) : (
                  <Save className="size-4" />
                )}
              </Button>
            )}
            {/* Cancel Button - Show when editing existing rule */}
            {isEditing && ruleId && (
              <Button
                variant="ghost"
                size="sm"
                onClick={e => { e.stopPropagation(); handleCancelEdit(); }}
                className="text-gray-600 hover:text-gray-700 hover:bg-gray-50"
                type="button"
                title="Cancel Edit"
              >
                Cancel
              </Button>
            )}
              <Badge variant={isDirty ? "secondary" : "outline"} className="text-xs">
                {isDirty && !ruleId ? "New Rule" : `Match Rule #${index + 1}`}
              </Badge>
            <div className="text-sm text-muted-foreground">
              {(rule.conditions || []).length} condition(s)
              {isDirty && <span className="text-amber-600 ml-2">• {ruleId ? "Editing" : "Unsaved"}</span>}
            </div>
          </button>

          <div className="flex items-center gap-2 ml-2">
            {/* Edit Button - Only show for saved rules that aren't being edited */}
            {ruleId && !isDirty && (
              <Button
                variant="ghost"
                size="sm"
                onClick={e => { e.stopPropagation(); handleEdit(); }}
                className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                type="button"
                title="Edit Rule"
              >
                <Edit className="size-4" />
              </Button>
            )}

            {/* Delete Button - Only show for saved rules */}
            {ruleId && onDelete && (
              <Button
                variant="ghost"
                size="sm"
                onClick={e => { e.stopPropagation(); handleDelete(); }}
                disabled={isDeleting}
                className="text-destructive hover:text-destructive"
                type="button"
                title="Delete Rule"
              >
                {isDeleting ? (
                  <Loader2 className="size-4 animate-spin" />
                ) : (
                  <Trash2 className="size-4" />
                )}
              </Button>
            )}

            {/* Remove Button - Only show for unsaved rules */}
            {!ruleId && canRemove && (
              <Button
                variant="ghost"
                size="sm"
                onClick={e => { e.stopPropagation(); handleRemove(); }}
                className="text-destructive hover:text-destructive"
                type="button"
                title="Remove Rule"
              >
                <Trash2 className="size-4"/>
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{height: 0, opacity: 0}}
            animate={{height: 'auto', opacity: 1}}
            exit={{height: 0, opacity: 0}}
            transition={{duration: 0.2}}
            style={{overflow: 'hidden'}}
          >
            <CardContent className="space-y-4 pt-0">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {/* Rule Name */}
                <div className="space-y-2">
                  <Label htmlFor={`rule-name-${ruleKey}`}>Rule Name</Label>
                  <Input
                    id={`rule-name-${ruleKey}`}
                    value={rule.name || ''}
                    onChange={handleNameChange}
                    placeholder="Enter rule name"
                    disabled={!isEditing || isSaving || isDeleting}
                    readOnly={!isEditing}
                  />
                  {!rule.name?.trim() && isDirty && (
                    <p className="text-xs text-amber-600">Rule name is required.</p>
                  )}
                </div>

                {/* Logic Operator */}
                <div className="space-y-2">
                  <Label htmlFor={`rule-operator-${ruleKey}`}>Logic Operator</Label>
                  <Select
                    value={rule.operator || 'and'}
                    onValueChange={handleOperatorChange}
                    disabled={!isEditing || isSaving || isDeleting}
                  >
                    <SelectTrigger>
                      <SelectValue/>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="and">AND (all conditions must match)</SelectItem>
                      <SelectItem value="or">OR (any condition can match)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor={`rule-description-${ruleKey}`}>Description</Label>
                <Textarea
                  id={`rule-description-${ruleKey}`}
                  value={rule.description || ''}
                  onChange={handleDescriptionChange}
                  placeholder="Describe what this rule matches"
                  rows={2}
                  disabled={!isEditing || isSaving || isDeleting}
                  readOnly={!isEditing}
                />
              </div>

              <Separator/>

              {/* Conditions */}
              <div className="space-y-3">
                <Label>Conditions</Label>
                <ConditionBuilder
                  conditions={rule.conditions || []}
                  onConditionsChange={handleConditionsChange}
                  allQuestions={allQuestions}
                  readOnly={!isEditing}
                />
                {(!rule.conditions || rule.conditions.length === 0) && isDirty && (
                  <p className="text-xs text-amber-600">At least one condition is required.</p>
                )}
              </div>

              {/* Validation Messages */}
              {!isValidRule && isDirty && (
                <div className="text-sm text-amber-600 bg-amber-50 p-3 rounded-lg border border-amber-200">
                  <p className="font-medium">Cannot save rule</p>
                  <ul className="text-xs mt-1 space-y-1">
                    {!rule.name?.trim() && <li>• Rule name is required</li>}
                    {(!rule.conditions || rule.conditions.length === 0) && <li>• At least one condition is required</li>}
                    {rule.conditions && rule.conditions.some(condition => {
                      if ('question_id' in condition) {
                        const question = allQuestions.find(q => (q._id || q.id) === condition.question_id);
                        if (!question) return true;
                        
                        if (question.type === 'number') {
                          return !condition.question_id || !condition.operator || (condition.value === '' || condition.value === null || condition.value === undefined);
                        } else {
                          return !condition.question_id || !condition.operator || !Array.isArray(condition.value) || condition.value.length === 0;
                        }
                      }
                      return false;
                    }) && <li>• All conditions must have a question, operator, and appropriate value</li>}
                  </ul>
                </div>
              )}
            </CardContent>
          </motion.div>
        )}
      </AnimatePresence>
    </Card>
  );
});

MatchRuleCard.displayName = 'MatchRuleCard';

export function MatchingConfigBuilder({
                                        form,
                                        matchRules = [],
                                        onAddMatchRule,
                                        onUpdateMatchRule,
                                        onRemoveMatchRule,
                                        onSaveMatchRule,
                                        onDeleteMatchRule,
                                        className
                                      }: MatchingConfigBuilderProps) {
  // Track expanded state for each rule using an array of indices
  const [expandedRuleIndices, setExpandedRuleIndices] = useState<number[]>([0]);
  const [isSavingAll, setIsSavingAll] = useState(false);
  const [isAddingRule, setIsAddingRule] = useState(false);

  // Get all questions from form for condition building - memoized and filtered to MCQ/Boolean/Number only
  const allQuestions = useMemo(() => {
    if (!form?.sections) return [];

    return form.sections.flatMap(section =>
      (section.questions || []).map(question => ({
        ...question,
        section_title: section.title
      }))
    ).filter(question => ['single_select', 'multi_select', 'boolean', 'number'].includes(question.type));
  }, [form]);

  // Check if there are any unsaved rules (rules without IDs)
  const hasUnsavedRules = useMemo(() => {
    return matchRules.some(rule => !rule._id && !rule.id);
  }, [matchRules]);

  // Helper to safely update expanded rules
  const updateExpandedRules = useCallback((index: number, isRemoving: boolean) => {
    setExpandedRuleIndices((prev) => {
      return prev
        .filter((i) => i !== index)
        .map((i) => (i > index && isRemoving ? i - 1 : i));
    });
  }, []);

  // Save all unsaved match rules
  const handleSaveAllMatchRules = useCallback(async () => {
    const unsavedRules = matchRules.filter(rule => !rule._id && !rule.id);
    
    if (unsavedRules.length === 0) {
      toast({
        title: "No changes to save",
        description: "All match rules are already saved.",
      });
      return;
    }

    setIsSavingAll(true);
    let savedCount = 0;
    let errorCount = 0;

    try {
      // Process rules sequentially to avoid race conditions
      for (const rule of unsavedRules) {
        try {
          // Validate before saving
          if (!rule.name?.trim()) {
            console.warn('Skipping rule without name:', rule);
            errorCount++;
            continue;
          }

          if (!rule.conditions || rule.conditions.length === 0) {
            console.warn('Skipping rule without conditions:', rule);
            errorCount++;
            continue;
          }

          await onSaveMatchRule(rule);
          savedCount++;
        } catch (error) {
          console.error('Error saving match rule:', error);
          errorCount++;
        }
      }

      if (savedCount > 0) {
        toast({
          title: "Match rules saved",
          description: `Successfully saved ${savedCount} match rule(s).${errorCount > 0 ? ` ${errorCount} failed to save.` : ''}`,
          variant: errorCount > 0 ? "destructive" : "default",
        });
      }
    } catch (error) {
      console.error('Error in save all operation:', error);
      toast({
        title: "Error saving match rules",
        description: "Failed to save match rules. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSavingAll(false);
    }
  }, [matchRules, onSaveMatchRule]);

  // Toggle rule expansion
  const toggleRuleExpansion = useCallback((index: number) => {
    setExpandedRuleIndices((prev) => {
      if (prev.includes(index)) {
        return prev.filter((i) => i !== index);
      } else {
        return [...prev, index];
      }
    });
  }, []);

  // Handle adding a new match rule - prevent duplicates
  const handleAddMatchRule = useCallback(() => {
    if (isAddingRule) return; // Prevent multiple rapid clicks

    try {
      setIsAddingRule(true);
      
      const newRule: Partial<MatchRule> = {
        name: `Match Rule #${matchRules.length + 1}`,
        description: 'Conditions for deal matching',
        operator: 'and',
        conditions: []
      };

      console.log('🎯 Adding new match rule:', newRule);
      onAddMatchRule(newRule);

      // Expand the new rule
      setExpandedRuleIndices((prev) => [...prev, matchRules.length]);
    } catch (error) {
      console.error('Error adding match rule:', error);
      toast({
        title: "Error",
        description: "Failed to add match rule. Please try again.",
        variant: "destructive",
      });
    } finally {
      // Reset after a short delay to prevent rapid clicking
      setTimeout(() => setIsAddingRule(false), 500);
    }
  }, [matchRules.length, onAddMatchRule, isAddingRule]);

  // Handle removing a match rule
  const handleRemoveMatchRule = useCallback((index: number) => {
    return () => {
      try {
        console.log('🗑️ Removing match rule at index:', index);

        if (index < 0 || !Array.isArray(matchRules) || index >= matchRules.length) {
          console.error('Invalid match rule index for removal:', index, 'Array length:', matchRules?.length);
          return;
        }

        onRemoveMatchRule(index);
        // Update expanded rules using the helper
        updateExpandedRules(index, true);
      } catch (error) {
        console.error('Error removing match rule:', error);
      }
    };
  }, [matchRules, onRemoveMatchRule, updateExpandedRules]);

  // Handle updating a match rule
  const handleUpdateMatchRule = useCallback((index: number) => {
    return (updates: Partial<MatchRule>) => {
      try {
        console.log('🎯 Updating match rule at index', index, 'with:', updates);

        if (index < 0 || !Array.isArray(matchRules) || index >= matchRules.length) {
          console.error('Invalid match rule index:', index, 'Array length:', matchRules?.length);
          return;
        }

        onUpdateMatchRule(index, updates);
      } catch (error) {
        console.error('Error updating match rule:', error);
      }
    };
  }, [matchRules, onUpdateMatchRule]);

  // Ensure matchRules is always an array
  const safeRules = useMemo(() => {
    return Array.isArray(matchRules) ? matchRules : [];
  }, [matchRules]);

  if (!form) {
    // If no form is loaded but we have existing match rules, show them
    if (matchRules && matchRules.length > 0) {
      return (
        <Card className={className}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="size-5"/>
              Deal Matching Rules
            </CardTitle>
            <p className="mt-1 text-sm text-muted-foreground">
              Form is loading... Existing rules are shown below.
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Existing Rules List - Show even without form */}
            <div className="space-y-4">
              <AnimatePresence mode="popLayout">
                {safeRules.map((rule, index) => (
                  <motion.div
                    key={rule.id || rule._id || `match-rule-${index}`}
                    layout
                    initial={{opacity: 0, y: 20}}
                    animate={{opacity: 1, y: 0}}
                    exit={{opacity: 0, y: -20}}
                    transition={{duration: 0.2}}
                  >
                    <MatchRuleCard
                      rule={rule}
                      index={index}
                      isExpanded={expandedRuleIndices.includes(index)}
                      onToggleExpansion={() => toggleRuleExpansion(index)}
                      onUpdate={handleUpdateMatchRule(index)}
                      onRemove={handleRemoveMatchRule(index)}
                      onSave={onSaveMatchRule}
                      onDelete={onDeleteMatchRule}
                      allQuestions={[]} // No questions available yet
                      canRemove={safeRules.length > 1}
                    />
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          </CardContent>
        </Card>
      );
    }

    // No form and no rules - show the original message
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="size-5"/>
            Deal Matching Rules
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <Info className="size-4"/>
            <AlertDescription>
              Please select a form first to define matching rules based on its questions.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Target className="size-5"/>
              Deal Matching Rules
            </CardTitle>
            <p className="mt-1 text-sm text-muted-foreground">
              Define which deals qualify for this thesis. Only deals that meet these conditions will be evaluated
              against your scoring criteria.
            </p>
          </div>
          <div className="flex items-center gap-2">
            {hasUnsavedRules && (
              <Button
                onClick={handleSaveAllMatchRules}
                disabled={isSavingAll}
                className="flex items-center gap-2"
                type="button"
              >
                {isSavingAll ? (
                  <>
                    <Loader2 className="size-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="size-4" />
                    Save All Changes
                  </>
                )}
              </Button>
            )}
            <Button
              onClick={handleAddMatchRule}
              variant="outline"
              size="sm"
              type="button"
              disabled={allQuestions.length === 0 || isAddingRule}
              title={allQuestions.length === 0 ? "No MCQ, Boolean, or Number questions available" : "Add matching rule"}
            >
              {isAddingRule ? (
                <Loader2 className="size-4 animate-spin" />
              ) : (
                <Plus className="size-4"/>
              )}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Info Banner */}
        <Alert>
          <Info className="size-4"/>
          <AlertDescription>
            Matching rules act as a filter - deals must satisfy these conditions to be considered for this thesis.
            Multiple Choice (single/multi-select), Boolean, and Number questions can be used for matching conditions.
          </AlertDescription>
        </Alert>

        {/* No MCQ/Boolean/Number Questions Warning */}
        {allQuestions.length === 0 && form?.sections && form.sections.length > 0 && (
          <Alert className="border-amber-200 bg-amber-50">
            <Info className="size-4 text-amber-600"/>
            <AlertDescription className="text-amber-800">
              <strong>No MCQ, Boolean, or Number questions available.</strong> Matching rules can only use Multiple Choice (single/multi-select), Boolean, or Number questions.
              Please add these question types to your form to create matching conditions.
            </AlertDescription>
          </Alert>
        )}
        
        {/* Rules List */}
        <div className="space-y-4">
          <AnimatePresence mode="popLayout">
            {safeRules.map((rule, index) => (
              <motion.div
                key={rule.id || rule._id || `match-rule-${index}`}
                layout
                initial={{opacity: 0, y: 20}}
                animate={{opacity: 1, y: 0}}
                exit={{opacity: 0, y: -20}}
                transition={{duration: 0.2}}
              >
                <MatchRuleCard
                  rule={rule}
                  index={index}
                  isExpanded={expandedRuleIndices.includes(index)}
                  onToggleExpansion={() => toggleRuleExpansion(index)}
                  onUpdate={handleUpdateMatchRule(index)}
                  onRemove={handleRemoveMatchRule(index)}
                  onSave={onSaveMatchRule}
                  onDelete={onDeleteMatchRule}
                  allQuestions={allQuestions}
                  canRemove={safeRules.length > 1}
                />
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {/* Empty State */}
        {safeRules.length === 0 && (
          <div className="rounded-lg bg-muted/50 py-8 text-center text-muted-foreground">
            <Target className="mx-auto mb-2 size-8 opacity-50"/>
            <p className="font-medium">No matching rules defined</p>
            <p className="text-sm">Add matching rules to filter which deals are eligible for this thesis.</p>
            <Button
              onClick={handleAddMatchRule}
              variant="outline"
              className="mt-4"
              type="button"
              disabled={allQuestions.length === 0 || isAddingRule}
            >
              {isAddingRule ? (
                <Loader2 className="size-4 animate-spin" />
              ) : (
                <Plus className="size-4"/>
              )}
              {allQuestions.length === 0 ? " No MCQ/Boolean/Number Questions" : " Add Matching Rule"}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
