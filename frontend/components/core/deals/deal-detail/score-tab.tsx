"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import {
  Target,
  Users,
  TrendingUp,
  TrendingDown,
  CheckCircle,
  ExternalLink,
  ChevronRight,
  Zap,
  AlertTriangle,
  Info,
  Star,
  Shield,
  Award,
  BarChart3,
  Calendar
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData } from "@/lib/types/deal-detail"
import { Deal } from "@/lib/types/deal"
import { EmptyPlaceholder } from "@/components/empty-placeholder"
import { ExclusionDisplay } from "./exclusion-display"
import { BonusPenaltySection } from "./bonus-penalty-section"
import { QuestionBreakdown } from "./question-breakdown"

interface ScoreTabProps {
  deal: DealDetailData | Deal
}

const getScoreBackground = (score: number) => {
  if (score >= 80) return 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200/50'
  if (score >= 60) return 'bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200/50'
  if (score >= 40) return 'bg-gradient-to-br from-yellow-50 to-orange-50 border-yellow-200/50'
  return 'bg-gradient-to-br from-red-50 to-red-50 border-red-200/50'
}

export function ScoreTab({ deal }: ScoreTabProps) {
  const [animatedScore, setAnimatedScore] = useState(0)

  // Extract comprehensive scoring data
  const comprehensiveScoring = (deal as any).scoring || (deal as any).comprehensive_scoring
  const thesisScoring = comprehensiveScoring?.thesis
  const exclusionResult = (deal as any).exclusion_filter_result
  const isExcluded = exclusionResult?.excluded || false

  // Calculate key metrics
  const thesisMatchPercent = thesisScoring?.normalized_score ? Math.round(thesisScoring.normalized_score * 100) : null
  const coreScore = thesisScoring?.core_score || 0
  const bonusTotal = thesisScoring?.bonus_total || 0
  const penaltyTotal = thesisScoring?.penalty_total || 0
  const maxPossibleScore = thesisScoring?.max_possible_score || 100
  const lastScored = comprehensiveScoring?.metadata?.scored_at

  // Animate score on mount
  useEffect(() => {
    if (thesisMatchPercent !== null) {
      const timer = setTimeout(() => {
        setAnimatedScore(thesisMatchPercent)
      }, 300)
      return () => clearTimeout(timer)
    }
  }, [thesisMatchPercent])

  const handleViewFullAnalysis = () => {
    window.location.href = `/deals/${deal.id}/full-analysis`
  }

  // Show exclusion first if deal is excluded
  if (isExcluded) {
    return (
      <div className="space-y-6">
        <ExclusionDisplay exclusionResult={exclusionResult} />

        {/* Still show any available scoring data for context */}
        {thesisScoring && (
          <Card className="border-0 shadow-sm bg-gray-50/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg text-gray-600">
                <BarChart3 className="h-5 w-5" />
                Scoring Data (Hidden due to exclusion)
              </CardTitle>
              <p className="text-sm text-gray-500">
                This deal was scored but results are hidden due to exclusion filter.
              </p>
            </CardHeader>
          </Card>
        )}
      </div>
    )
  }

  // Show empty state if no scoring data
  if (!thesisScoring || thesisMatchPercent === null) {
    return (
      <EmptyPlaceholder>
        <EmptyPlaceholder.Icon name="target" />
        <EmptyPlaceholder.Title>No scoring data available</EmptyPlaceholder.Title>
        <EmptyPlaceholder.Description>
          This deal hasn't been scored yet. Scoring will appear here once the analysis is complete.
        </EmptyPlaceholder.Description>
      </EmptyPlaceholder>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-3">
              <Target className="h-6 w-6 text-blue-600" />
              <h2 className="text-2xl font-bold text-gray-900">Thesis Match Analysis</h2>
            </div>
            {lastScored && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Calendar className="h-4 w-4" />
                <span>Last scored {new Date(lastScored * 1000).toLocaleDateString()}</span>
              </div>
            )}
          </div>
          <Button
            onClick={handleViewFullAnalysis}
            size="sm"
            className="gap-2"
          >
            View Full Analysis
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* Main Thesis Match Score Card */}
        <Card className={cn(
          "p-6 border-0 shadow-lg",
          getScoreBackground(thesisMatchPercent)
        )}>
          <CardContent className="p-0">
            <div className="flex items-center justify-between">
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-4">
                    <motion.span
                      className="text-5xl font-bold text-gray-900"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.8 }}
                    >
                      {animatedScore}%
                    </motion.span>
                    <div>
                      <p className="text-lg font-semibold text-gray-900">
                        Thesis Match
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Based on {Object.keys(thesisScoring.question_scores || {}).length} criteria
                      </p>
                    </div>
                  </div>

                  <div className="w-80">
                    <Progress
                      value={animatedScore}
                      className="h-3"
                    />
                  </div>
                </div>
              </div>

              {/* Status Badges */}
              <div className="flex flex-col gap-2 items-end">
                <Badge className={cn(
                  "text-sm font-bold px-4 py-2",
                  thesisMatchPercent >= 80 ? "bg-green-100 text-green-800 border-green-200" :
                  thesisMatchPercent >= 60 ? "bg-blue-100 text-blue-800 border-blue-200" :
                  thesisMatchPercent >= 40 ? "bg-yellow-100 text-yellow-800 border-yellow-200" :
                  "bg-red-100 text-red-800 border-red-200"
                )}>
                  {thesisMatchPercent >= 80 ? "Strong Match" :
                   thesisMatchPercent >= 60 ? "Good Match" :
                   thesisMatchPercent >= 40 ? "Partial Match" : "Weak Match"}
                </Badge>

                {(bonusTotal > 0 || penaltyTotal > 0) && (
                  <Badge variant="outline" className="text-xs">
                    {bonusTotal > 0 && `+${bonusTotal} bonus`}
                    {bonusTotal > 0 && penaltyTotal > 0 && ", "}
                    {penaltyTotal > 0 && `-${penaltyTotal} penalty`}
                  </Badge>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Score Summary Section */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card className="border-0 shadow-sm bg-white/80 backdrop-blur-sm">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-blue-100 text-blue-600">
                <BarChart3 className="h-5 w-5" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Core Score</p>
                <p className="text-2xl font-bold text-gray-900">
                  {coreScore.toFixed(1)} / {maxPossibleScore}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {bonusTotal > 0 && (
          <Card className="border-0 shadow-sm bg-green-50/80 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-green-100 text-green-600">
                  <TrendingUp className="h-5 w-5" />
                </div>
                <div>
                  <p className="text-sm font-medium text-green-600">Bonus Points</p>
                  <p className="text-2xl font-bold text-green-900">+{bonusTotal}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {penaltyTotal > 0 && (
          <Card className="border-0 shadow-sm bg-red-50/80 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-red-100 text-red-600">
                  <TrendingDown className="h-5 w-5" />
                </div>
                <div>
                  <p className="text-sm font-medium text-red-600">Penalty Points</p>
                  <p className="text-2xl font-bold text-red-900">-{penaltyTotal}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Bonus/Penalty Section */}
      {thesisScoring.bonus_scores && Object.keys(thesisScoring.bonus_scores).length > 0 && (
        <BonusPenaltySection bonusScores={thesisScoring.bonus_scores} />
      )}

      {/* Detailed Question Breakdown */}
      <Card className="border-0 shadow-sm bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-xl">
            <Award className="h-5 w-5 text-blue-600" />
            Question-by-Question Analysis
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Detailed breakdown of how this deal scores against each thesis criterion
          </p>
        </CardHeader>
        <CardContent>
          {thesisScoring.question_scores && Object.keys(thesisScoring.question_scores).length > 0 ? (
            <QuestionBreakdown questionScores={thesisScoring.question_scores} />
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Target className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No question-level scoring data available</p>
            </div>
          )}
        </CardContent>
      </Card>

    </div>
  )
}
