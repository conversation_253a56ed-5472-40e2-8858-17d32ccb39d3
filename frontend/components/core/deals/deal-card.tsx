"use client"

import Link from 'next/link';
import { motion } from 'framer-motion';
import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { But<PERSON> } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { visualRetreat } from '@/lib/utils/responsive';
import { Deal, DealStatus } from '@/lib/types/deal';
import { getDealId } from '@/lib/utils/deal-id';
import {
  MoreHorizontal,
  ExternalLink,
  MapPin,
  Flag,
  Archive,
  Trash2,
  Target,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Info
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';

interface DealCardProps {
  deal: Deal;
  index: number;
  onClick?: (deal: Deal) => void;
  onDelete?: (dealId: string) => Promise<void>;
}

// Backend-aligned utility functions
const getStatusColor = (status: DealStatus) => {
  switch (status) {
    case DealStatus.NEW:
      return 'bg-blue-50 text-blue-700 border-blue-200';
    case DealStatus.TRIAGE:
      return 'bg-yellow-50 text-yellow-700 border-yellow-200';
    case DealStatus.REVIEWED:
      return 'bg-purple-50 text-purple-700 border-purple-200';
    case DealStatus.APPROVED:
      return 'bg-green-50 text-green-700 border-green-200';
    case DealStatus.NEGOTIATING:
      return 'bg-orange-50 text-orange-700 border-orange-200';
    case DealStatus.CLOSED:
      return 'bg-gray-50 text-gray-700 border-gray-200';
    case DealStatus.EXCLUDED:
    case DealStatus.REJECTED:
      return 'bg-red-50 text-red-700 border-red-200';
    default:
      return 'bg-gray-50 text-gray-700 border-gray-200';
  }
};

const getScoreColor = (score: number) => {
  if (score >= 80) return 'bg-green-50 text-green-700 border-green-200';
  if (score >= 50) return 'bg-blue-50 text-blue-700 border-blue-200';
  return 'bg-yellow-50 text-yellow-700 border-yellow-200';
};

// Enhanced scoring utilities for thesis match display
const getThesisMatchColor = (percent: number) => {
  if (percent >= 80) return 'bg-green-500 text-white';
  if (percent >= 60) return 'bg-blue-500 text-white';
  if (percent >= 40) return 'bg-yellow-500 text-white';
  return 'bg-red-500 text-white';
};

const getBonusPenaltyColor = (points: number) => {
  if (points > 0) return 'bg-green-100 text-green-700 border-green-200';
  if (points < 0) return 'bg-red-100 text-red-700 border-red-200';
  return 'bg-gray-100 text-gray-700 border-gray-200';
};

const getExclusionIcon = (excluded: boolean) => {
  return excluded ? XCircle : CheckCircle;
};

const getExclusionColor = (excluded: boolean) => {
  return excluded
    ? 'text-red-600 bg-red-50 border-red-200'
    : 'text-green-600 bg-green-50 border-green-200';
};

const getAvatarColor = (name: string) => {
  const colors = [
    'bg-blue-500 text-white',
    'bg-green-500 text-white',
    'bg-purple-500 text-white',
    'bg-orange-500 text-white',
    'bg-pink-500 text-white',
    'bg-indigo-500 text-white',
    'bg-teal-500 text-white',
    'bg-red-500 text-white',
  ];

  const hash = name.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);

  return colors[Math.abs(hash) % colors.length];
};

const formatSector = (sector: string | string[] | undefined): string[] => {
  if (!sector) return [];
  if (Array.isArray(sector)) return sector;
  return [sector];
};

const createSummary = (deal: Deal): string => {
  const sectors = formatSector(deal.sector);
  const parts: string[] = [];

  if (sectors.length > 0) {
    parts.push(`${sectors[0]} company`);
  }

  if (deal.stage) {
    parts.push(`at ${deal.stage} stage`);
  }

  if (parts.length === 0) {
    return 'Investment opportunity in review';
  }

  return parts.join(' ') + '.';
};

export function DealCard({ deal, index, onClick, onDelete }: DealCardProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut",
        delay: index * 0.05
      }
    }
  };

  // Get normalized deal ID - critical for navigation
  const dealId = getDealId(deal);

  // Extract backend data with proper fallbacks
  const companyName = deal.company_name || 'Unnamed Company';
  const sectors = formatSector(deal.sector);
  const stage = deal.stage;
  const website = deal.company_website;
  const status = deal.status;
  const tags = deal.tags || [];

  // Enhanced scoring data extraction
  const thesisScoring = deal.scoring?.thesis;
  const exclusionResult = deal.exclusion_filter_result;
  const thesisMatchPercent = thesisScoring?.normalized_score ? Math.round(thesisScoring.normalized_score * 100) : null;
  const coreScore = thesisScoring?.core_score;
  const bonusTotal = thesisScoring?.bonus_total || 0;
  const penaltyTotal = thesisScoring?.penalty_total || 0;
  const netBonusPenalty = bonusTotal - penaltyTotal;
  const isExcluded = exclusionResult?.excluded || false;
  const exclusionReason = exclusionResult?.reason;

  // Prevent navigation if no valid ID
  if (!dealId) {
    console.error('Deal card missing valid ID:', deal);
    return null;
  }

  const handleDelete = async () => {
    if (!onDelete) return;

    setDeleting(true);
    try {
      await onDelete(dealId);
      setDeleteDialogOpen(false);
    } catch (error) {
      console.error('Error deleting deal:', error);
    } finally {
      setDeleting(false);
    }
  };

  // Generate company initials for avatar
  const initials = companyName
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');

  const avatarColor = getAvatarColor(companyName);
  const summary = createSummary(deal);

  // Extract country from enriched_data or tags (placeholder logic)
  const country = deal.enriched_data?.country ||
                 tags.find(tag => tag.includes('Country:'))?.replace('Country:', '') ||
                 null;

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      className="group"
    >
      <Card className={cn(
        "group cursor-pointer transition-all duration-300 ease-out",
        "bg-white/98 backdrop-blur-sm border border-gray-200/50",
        "rounded-2xl shadow-sm hover:shadow-xl",
        "hover:scale-[1.02] active:scale-[0.98]",
        "hover:bg-white hover:border-gray-300/50"
      )}>
        <Link href={`/deals/${dealId}`} className="block">
          <CardContent className="p-8 md:p-10 flex flex-col gap-6">
            {/* Header Row */}
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-6 flex-1 min-w-0">
                {/* Avatar */}
                <Avatar className="h-16 w-16 flex-shrink-0 shadow-sm ring-2 ring-gray-100">
                  <AvatarFallback className={cn("text-lg font-bold", avatarColor)}>
                    {initials}
                  </AvatarFallback>
                </Avatar>

                {/* Company Info */}
                <div className="flex-1 min-w-0 pt-1">
                  <h3 className="text-2xl font-bold text-gray-900 leading-tight mb-4 truncate group-hover:text-blue-600 transition-colors">
                    {companyName}
                  </h3>

                  {/* Status and Stage Chips */}
                  <div className="flex items-center gap-3 flex-wrap">
                    <Badge
                      className={cn("text-xs font-semibold px-4 py-2 rounded-full", getStatusColor(status))}
                    >
                      {status.charAt(0).toUpperCase() + status.slice(1).toLowerCase()}
                    </Badge>
                    {stage && (
                      <Badge
                        variant="secondary"
                        className="text-xs font-semibold bg-gray-100 text-gray-700 px-4 py-2 rounded-full"
                      >
                        {stage}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>

              {/* Actions Menu - Show on hover */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-10 w-10 p-0 opacity-0 group-hover:opacity-100 transition-all duration-300 flex-shrink-0 hover:bg-gray-100 rounded-xl"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                  >
                    <MoreHorizontal className="h-5 w-5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-52">
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                  >
                    <Flag className="h-4 w-4 mr-2" />
                    Flag Deal
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                  >
                    <Archive className="h-4 w-4 mr-2" />
                    Archive
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      setDeleteDialogOpen(true);
                    }}
                    className="text-red-600 focus:text-red-600"
                    disabled={!onDelete}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Sector Tags */}
            {sectors.length > 0 && (
              <div className="flex flex-wrap gap-3">
                {sectors.slice(0, 2).map((sector, idx) => (
                  <Badge
                    key={idx}
                    variant="secondary"
                    className="text-xs font-semibold bg-gray-100 text-gray-700 px-4 py-2 rounded-full"
                  >
                    {sector}
                  </Badge>
                ))}
                {sectors.length > 2 && (
                  <Badge
                    variant="secondary"
                    className="text-xs font-semibold bg-gray-100 text-gray-700 px-4 py-2 rounded-full"
                  >
                    +{sectors.length - 2} more
                  </Badge>
                )}
              </div>
            )}

            {/* Summary */}
            <p className="text-base text-gray-700 leading-relaxed">
              {summary}
            </p>

            {/* Enhanced Bottom Row - Thesis Match, Bonus/Penalty, Exclusion Status */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3 flex-wrap">
                {/* Thesis Match Percentage - Main Score Badge */}
                {!isExcluded && thesisMatchPercent !== null && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Badge
                          className={cn(
                            "text-lg font-bold px-4 py-2 rounded-full shadow-sm",
                            getThesisMatchColor(thesisMatchPercent)
                          )}
                        >
                          <Target className="h-4 w-4 mr-2" />
                          {thesisMatchPercent}% Match
                        </Badge>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="text-sm">
                          <p className="font-medium">Thesis Match Score</p>
                          <p>Core: {coreScore?.toFixed(1) || 'N/A'}</p>
                          {netBonusPenalty !== 0 && (
                            <p>Bonus/Penalty: {netBonusPenalty > 0 ? '+' : ''}{netBonusPenalty}</p>
                          )}
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}

                {/* Bonus/Penalty Indicator */}
                {!isExcluded && netBonusPenalty !== 0 && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Badge
                          className={cn(
                            "text-xs font-semibold px-3 py-1 rounded-full",
                            getBonusPenaltyColor(netBonusPenalty)
                          )}
                        >
                          {netBonusPenalty > 0 ? (
                            <>
                              <TrendingUp className="h-3 w-3 mr-1" />
                              Bonus +{netBonusPenalty}
                            </>
                          ) : (
                            <>
                              <TrendingDown className="h-3 w-3 mr-1" />
                              Penalty {netBonusPenalty}
                            </>
                          )}
                        </Badge>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="text-sm">
                          <p className="font-medium">
                            {netBonusPenalty > 0 ? 'Bonus Points' : 'Penalty Points'}
                          </p>
                          {bonusTotal > 0 && <p>Bonus: +{bonusTotal}</p>}
                          {penaltyTotal > 0 && <p>Penalty: -{penaltyTotal}</p>}
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}

                {/* Exclusion Status */}
                {isExcluded && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Badge
                          className={cn(
                            "text-sm font-bold px-4 py-2 rounded-full",
                            getExclusionColor(true)
                          )}
                        >
                          <Flag className="h-4 w-4 mr-2" />
                          Excluded
                        </Badge>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="text-sm max-w-xs">
                          <p className="font-medium">Excluded by filter</p>
                          {exclusionReason && <p className="mt-1">{exclusionReason}</p>}
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}

                {/* Website Link */}
                {website && (
                  <Badge
                    variant="outline"
                    className="text-xs text-gray-700 bg-gray-50 border-gray-200 px-3 py-1 flex items-center gap-2 cursor-pointer hover:bg-gray-100 transition-all duration-200 font-semibold shadow-sm rounded-full"
                    onClick={(e) => {
                      e.preventDefault();
                      window.open(website.startsWith('http') ? website : `https://${website}`, '_blank');
                    }}
                  >
                    <ExternalLink className="h-3 w-3" />
                    <span>Website</span>
                  </Badge>
                )}
              </div>

              {/* Country */}
              {country && (
                <div className="flex items-center gap-2 text-sm text-gray-500 font-semibold">
                  <MapPin className="h-4 w-4" />
                  <span>{country}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Link>
      </Card>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={deleteDialogOpen}
        title="Delete Deal"
        description={`Are you sure you want to delete the deal "${companyName}"? This action cannot be undone.`}
        onCancel={() => setDeleteDialogOpen(false)}
        onConfirm={handleDelete}
        loading={deleting}
      />
    </motion.div>
  );
}
