import * as React from "react"
import Link from "next/link"

import { MainNavItem } from "@/types"
import { siteConfig } from "@/config/site"
import { cn } from "@/lib/utils"
import { visualRetreat } from "@/lib/utils/responsive"
import { useLockBody } from "@/hooks/use-lock-body"
import { Icons } from "@/components/icons"

interface MobileNavProps {
  items: MainNavItem[]
  children?: React.ReactNode
}

export function MobileNav({ items, children }: MobileNavProps) {
  useLockBody()

  return (
    <div
      className={cn(
        // Mobile-first navigation overlay
        "fixed inset-0 z-50 bg-background/80 backdrop-blur-sm",
        "animate-in fade-in-0 duration-300",
        "md:hidden" // Hide on desktop
      )}
    >
      {/* Navigation Panel */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-50 w-full max-w-sm bg-background border-r shadow-lg",
          "animate-in slide-in-from-left-full duration-300",
          "safe-top safe-bottom" // Respect device safe areas
        )}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <Link href="/" className="flex items-center space-x-2">
            <Icons.logo className="h-6 w-6" />
            <span className="font-bold text-lg">{siteConfig.name}</span>
          </Link>
        </div>

        {/* Navigation Items */}
        <nav className="flex-1 overflow-y-auto p-6">
          <div className="space-y-2">
            {items.map((item, index) => (
              <Link
                key={index}
                href={item.disabled ? "#" : item.href}
                className={cn(
                  // Mobile-first navigation item styling
                  "flex w-full items-center rounded-xl p-4 text-base font-medium",
                  "transition-all duration-200 touch-target",
                  "hover:bg-accent hover:text-accent-foreground",
                  "active:scale-[0.98]",
                  item.disabled && "cursor-not-allowed opacity-60"
                )}
              >
                {item.title}
              </Link>
            ))}
          </div>

          {/* Additional Content */}
          {children && (
            <div className="mt-8 pt-6 border-t">
              {children}
            </div>
          )}
        </nav>
      </div>
    </div>
  )
}
