"""
Deal Service Interface

Defines the contract for deal services, ensuring consistent implementation across different backends.
"""

from typing import Any, Dict, List, Optional, Protocol, Tuple, Union

from bson import ObjectId

from app.models.deal import Deal, DealStatus
from app.models.form import FormWithDetails


class DealServiceInterface(Protocol):
    """Interface for deal services."""

    async def initialize(self) -> None:
        """Initialize service dependencies."""
        ...

    async def cleanup(self) -> None:
        """Cleanup service resources."""
        ...

    # Core CRUD Operations
    async def create_deal(
        self,
        org_id: Union[str, ObjectId],
        form_id: Union[str, ObjectId],
        submission_id: Union[str, ObjectId],
        created_by: Union[str, ObjectId],
        deal_data: Optional[Dict[str, Any]] = None,
    ) -> Optional[Deal]:
        """
        Create a new deal directly.

        Args:
            org_id: Organization ID
            form_id: Form ID
            submission_id: Primary submission ID
            created_by: User ID who created the deal
            deal_data: Optional deal data (core fields, notes, tags, etc.)

        Returns:
            Created deal or None if creation fails
        """
        ...

    async def create_deal_from_submission(
        self,
        form_id: Union[str, ObjectId],
        submission_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        created_by: Union[str, ObjectId],
        form: Optional[FormWithDetails] = None,
        submission_data: Optional[Dict[str, Any]] = None,
        thesis_id: Optional[Union[str, ObjectId]] = None,
    ) -> Optional[Deal]:
        """
        Create a new deal from a form submission.

        Args:
            form_id: ID of the form
            submission_id: ID of the submission
            org_id: Organization ID
            created_by: User ID who created the deal
            form: Optional form object (will be fetched if not provided)
            submission_data: Optional submission data (will be fetched if not provided)
            thesis_id: Optional thesis ID for scoring

        Returns:
            Created deal or None if creation fails
        """
        ...

    async def get_deal(self, deal_id: Union[str, ObjectId]) -> Optional[Deal]:
        """
        Get a deal by ID.

        Args:
            deal_id: Deal ID

        Returns:
            Deal if found, None otherwise
        """
        ...

    async def list_deals(
        self,
        org_id: Union[str, ObjectId],
        skip: int = 0,
        limit: int = 100,
        status: Optional[DealStatus] = None,
        form_id: Optional[Union[str, ObjectId]] = None,
        search: Optional[str] = None,
        stage: Optional[str] = None,
        sector: Optional[str] = None,
        tags: Optional[List[str]] = None,
        sort_by: str = "created_at",
        sort_order: str = "desc",
    ) -> Tuple[List[Deal], int]:
        """
        List deals with optional filtering and pagination.

        Args:
            org_id: Organization ID
            skip: Number of records to skip
            limit: Maximum number of records to return
            status: Optional status filter
            form_id: Optional form ID filter
            search: Optional search term for company name
            stage: Optional stage filter
            sector: Optional sector filter
            tags: Optional tags filter
            sort_by: Field to sort by
            sort_order: Sort order (asc/desc)

        Returns:
            Tuple of (deals list, total count)
        """
        ...

    async def update_deal(
        self, deal_id: Union[str, ObjectId], update_data: Dict[str, Any]
    ) -> Optional[Deal]:
        """
        Update a deal.

        Args:
            deal_id: Deal ID
            update_data: Dictionary of fields to update

        Returns:
            Updated deal or None if update fails
        """
        ...

    async def delete_deal(self, deal_id: Union[str, ObjectId]) -> bool:
        """
        Delete a deal.

        Args:
            deal_id: Deal ID

        Returns:
            True if deleted, False otherwise
        """
        ...

    # Auxiliary Operations
    async def get_deals_by_submission(
        self, submission_id: Union[str, ObjectId], org_id: Union[str, ObjectId]
    ) -> List[Deal]:
        """
        Get deals associated with a specific submission.

        Args:
            submission_id: Submission ID
            org_id: Organization ID

        Returns:
            List of deals
        """
        ...

    async def get_deals_by_form(
        self,
        form_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        skip: int = 0,
        limit: int = 100,
    ) -> Tuple[List[Deal], int]:
        """
        Get deals associated with a specific form.

        Args:
            form_id: Form ID
            org_id: Organization ID
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            Tuple of (deals list, total count)
        """
        ...

    async def get_deal_summary(self, org_id: Union[str, ObjectId]) -> Dict[str, Any]:
        """
        Get deal summary statistics for dashboard.

        Args:
            org_id: Organization ID

        Returns:
            Dictionary with summary statistics
        """
        ...

    async def add_timeline_event(
        self,
        deal_id: Union[str, ObjectId],
        event: str,
        notes: Optional[str] = None,
        user_id: Optional[Union[str, ObjectId]] = None,
    ) -> Optional[Deal]:
        """
        Add a timeline event to a deal.

        Args:
            deal_id: Deal ID
            event: Event description
            notes: Optional notes
            user_id: Optional user ID who triggered the event

        Returns:
            Updated deal or None if update fails
        """
        ...

    async def update_deal_notes(
        self, deal_id: Union[str, ObjectId], notes: str
    ) -> Optional[Deal]:
        """
        Update deal notes.

        Args:
            deal_id: Deal ID
            notes: New notes

        Returns:
            Updated deal or None if update fails
        """
        ...

    async def bulk_update_deals(
        self,
        deal_ids: List[Union[str, ObjectId]],
        update_data: Dict[str, Any],
        org_id: Union[str, ObjectId],
    ) -> List[Deal]:
        """
        Bulk update multiple deals.

        Args:
            deal_ids: List of deal IDs to update
            update_data: Dictionary of fields to update
            org_id: Organization ID (for security)

        Returns:
            List of updated deals
        """
        ...

    async def search_deals(
        self,
        org_id: Union[str, ObjectId],
        query: Optional[str] = None,
        filters: Optional[Dict[str, List[str]]] = None,
        date_range: Optional[Dict[str, str]] = None,
        sort_by: str = "created_at",
        sort_order: str = "desc",
        skip: int = 0,
        limit: int = 100,
    ) -> Tuple[List[Deal], int]:
        """
        Advanced search for deals.

        Args:
            org_id: Organization ID
            query: Search query for company name
            filters: Dictionary of filters to apply
            date_range: Date range filter
            sort_by: Field to sort by
            sort_order: Sort order (asc/desc)
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            Tuple of (deals list, total count)
        """
        ...

    async def create_deal_with_invite(
        self,
        org_id: Union[str, ObjectId],
        form_id: Union[str, ObjectId],
        created_by: Union[str, ObjectId],
        company_name: str,
        invited_email: str,
        company_website: Optional[str] = None,
        pitch_deck_s3_key: Optional[str] = None,
        notes: Optional[str] = None,
        stage: Optional[str] = None,
        sector: Optional[str] = None,
    ) -> Optional[Deal]:
        pass

    async def _queue_invite_email(self, deal: Deal) -> None:
        pass

    async def _generate_context_block(self, deal: Deal) -> None:
        pass

    async def update_invite_status(
        self,
        deal_id: Union[str, ObjectId],
        status: str,
        submission_id: Optional[str] = None,
    ) -> Optional[Deal]:
        pass

    async def get_deal_submission_preview(
        self, deal_id: Union[str, ObjectId]
    ) -> Optional[Dict[str, Any]]:
        """
        Get a comprehensive preview of all submissions for a deal.

        Returns structured data with form questions mapped to human-readable labels,
        repeatable sections handled properly, and metadata for frontend rendering.

        Args:
            deal_id: Deal ID

        Returns:
            Dictionary containing:
            - deal_id: Deal ID
            - no_submissions: Boolean indicating if there are no submissions
            - submissions: List of submission previews with mapped answers
            - dropdown: List of dropdown options for frontend
        """
        ...
