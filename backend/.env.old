# API Settings
API_V1_STR=/api/v1
PROJECT_NAME=X-App
VERSION=0.1.0
DESCRIPTION=Investing intelligence platform backend

# Security
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
ALGORITHM=HS256
TEST_MODE=False
INVITATION_TOKEN_EXPIRY=900
PASSWORD_RESET_TOKEN_EXPIRY=900

# CORS
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8000","http://localhost:8080","http://localhost:3000/","https://v1.tractionx.ai","https://v1.tractionx.ai/"]

# MongoDB
MONGODB_URL=mongodb+srv://prasanna:<EMAIL>
MONGODB_DB_NAME=tx_mvp_prod

# Redis
REDIS_URL=redis://default:<EMAIL>:14410
REDIS_KEY_PREFIX=tx_backend
REDIS_DEFAULT_TTL=3600

# Queue
WORKER_CONCURRENCY=2

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Reset token expiry
RESET_TOKEN_EXPIRY=120

# Organization Settings
DOMAIN=tractionx.ai
ALLOWED_HOSTS=["localhost","127.0.0.1","tractionx.ai"]

# Portal URLs
BASIC_PORTAL_URL=https://app.tractionx.com
FRONTEND_URL=https://app.tractionx.com

# Email Settings
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM=<EMAIL>
SMTP_FROM_NAME=TractionX

# Resend
RESEND_API_KEY=re_5cY1fEu6_HfWWNnjhJR19onYSwEvX891a
RESEND_DOMAIN=signups.tractionx.ai

# AWS
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=4McRoKBUaibE6TfXPsESsPLOXYVYRFoCc63S274Z
AWS_REGION=ap-southeast-1
S3_BUCKET_SUBMISSIONS=tx-prod-mvp-datalake
S3_PRESIGNED_URL_EXPIRY=600

# File Upload Limits
MAX_FILE_SIZE_MB=50

# OpenAI
OPENAI_API_KEY=********************************************************************************************************************************************************************

# Perlexity:
PERPLEXITY_API_KEY=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
USE_RESEND=True
