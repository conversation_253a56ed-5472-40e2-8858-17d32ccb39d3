"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { TrendingUp, TrendingDown, Zap, CheckCircle, XCircle, AlertTriangle } from "lucide-react"
import { cn } from "@/lib/utils"
import { visualRetreat, mobileRetreat } from "@/lib/utils/responsive"
import { BonusScore } from "@/lib/types/deal"

interface BonusPenaltySectionProps {
  bonusScores: Record<string, BonusScore>
  className?: string
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'awarded':
      return CheckCircle
    case 'blocked':
    case 'failed':
      return XCircle
    default:
      return AlertTriangle
  }
}

const getStatusColor = (status: string, ruleType: string) => {
  const isBonus = ruleType === 'BONUS'
  
  switch (status) {
    case 'awarded':
      return isBonus 
        ? 'text-green-600 bg-green-50 border-green-200'
        : 'text-red-600 bg-red-50 border-red-200'
    case 'blocked':
    case 'failed':
      return 'text-gray-600 bg-gray-50 border-gray-200'
    default:
      return 'text-yellow-600 bg-yellow-50 border-yellow-200'
  }
}

const getRuleTypeIcon = (ruleType: string) => {
  return ruleType === 'BONUS' ? TrendingUp : TrendingDown
}

const getRuleTypeColor = (ruleType: string) => {
  return ruleType === 'BONUS' 
    ? 'text-green-600 bg-green-100 border-green-200'
    : 'text-red-600 bg-red-100 border-red-200'
}

export function BonusPenaltySection({ bonusScores, className }: BonusPenaltySectionProps) {
  const bonusEntries = Object.entries(bonusScores)
  
  if (bonusEntries.length === 0) {
    return null
  }

  // Separate bonuses and penalties
  const bonuses = bonusEntries.filter(([_, score]) => score.rule_type === 'BONUS')
  const penalties = bonusEntries.filter(([_, score]) => score.rule_type === 'PENALTY')

  // Calculate totals
  const totalBonusAwarded = bonuses
    .filter(([_, score]) => score.status === 'awarded')
    .reduce((sum, [_, score]) => sum + score.points, 0)
  
  const totalPenaltyAwarded = penalties
    .filter(([_, score]) => score.status === 'awarded')
    .reduce((sum, [_, score]) => sum + score.points, 0)

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={className}
    >
      <Card className={cn("border-0 shadow-sm", visualRetreat.card.base)}>
        <CardHeader className="p-6">
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div>
              <CardTitle className="flex items-center gap-3 text-xl md:text-2xl">
                <Zap className="h-6 w-6 text-blue-600" />
                Bonus & Penalty Rules
              </CardTitle>
              <p className="text-sm md:text-base text-muted-foreground mt-2">
                Additional points awarded or deducted based on specific criteria
              </p>
            </div>

            <div className="flex items-center gap-3 flex-wrap">
              {totalBonusAwarded > 0 && (
                <Badge className="bg-green-100 text-green-800 border-green-200 px-3 py-2 rounded-full">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  +{totalBonusAwarded} pts
                </Badge>
              )}
              {totalPenaltyAwarded > 0 && (
                <Badge className="bg-red-100 text-red-800 border-red-200 px-3 py-2 rounded-full">
                  <TrendingDown className="h-4 w-4 mr-2" />
                  -{totalPenaltyAwarded} pts
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="p-6 space-y-6">
          {bonusEntries.map(([ruleId, bonusData], index) => {
            const StatusIcon = getStatusIcon(bonusData.status)
            const RuleTypeIcon = getRuleTypeIcon(bonusData.rule_type)

            return (
              <motion.div
                key={ruleId}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className={cn(
                  "flex flex-col gap-4 p-6 rounded-xl border",
                  "md:flex-row md:items-start md:gap-6",
                  getStatusColor(bonusData.status, bonusData.rule_type)
                )}
              >
                <div className="flex items-center gap-3 flex-shrink-0">
                  <div className={cn(
                    "p-3 rounded-xl border",
                    getRuleTypeColor(bonusData.rule_type)
                  )}>
                    <RuleTypeIcon className="h-5 w-5" />
                  </div>
                  <StatusIcon className="h-5 w-5" />
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex flex-col gap-4 md:flex-row md:items-start md:justify-between">
                    <div className="flex-1">
                      <h4 className="font-semibold text-base md:text-lg">
                        {bonusData.rule_name || `${bonusData.rule_type} Rule`}
                      </h4>
                      <p className="text-sm md:text-base mt-2 leading-relaxed">
                        {bonusData.reason}
                      </p>
                      {bonusData.explanation && bonusData.explanation !== bonusData.reason && (
                        <p className="text-xs md:text-sm text-muted-foreground mt-3">
                          {bonusData.explanation}
                        </p>
                      )}
                    </div>

                    <div className="flex items-center gap-3 md:flex-col md:items-end">
                      <Badge
                        variant="outline"
                        className={cn(
                          "text-sm font-bold px-3 py-2 rounded-full",
                          bonusData.status === 'awarded'
                            ? bonusData.rule_type === 'BONUS'
                              ? "border-green-300 text-green-700"
                              : "border-red-300 text-red-700"
                            : "border-gray-300 text-gray-600"
                        )}
                      >
                        {bonusData.rule_type === 'BONUS' ? '+' : '-'}{Math.abs(bonusData.points)} pts
                      </Badge>

                      <Badge
                        variant="secondary"
                        className="text-sm capitalize px-3 py-1 rounded-full"
                      >
                        {bonusData.status}
                      </Badge>
                    </div>
                  </div>
                </div>
              </motion.div>
            )
          })}
          
          {/* Summary */}
          {(totalBonusAwarded > 0 || totalPenaltyAwarded > 0) && (
            <div className="pt-6 border-t border-gray-200">
              <div className="flex items-center justify-between text-base md:text-lg">
                <span className="font-medium text-gray-700">Net Impact:</span>
                <span className={cn(
                  "font-bold text-lg md:text-xl",
                  (totalBonusAwarded - totalPenaltyAwarded) > 0
                    ? "text-green-600"
                    : (totalBonusAwarded - totalPenaltyAwarded) < 0
                      ? "text-red-600"
                      : "text-gray-600"
                )}>
                  {totalBonusAwarded - totalPenaltyAwarded > 0 ? '+' : ''}
                  {totalBonusAwarded - totalPenaltyAwarded} points
                </span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}
