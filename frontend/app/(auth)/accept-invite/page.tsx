import { Metada<PERSON> } from "next"
import { Suspense } from "react"

import { AuthLayout } from "@/components/auth/auth-layout"
import { AcceptInviteForm } from "@/components/auth/accept-invite-form"
import { visualRetreat, mobileRetreat } from "@/lib/utils/responsive"

export const metadata: Metadata = {
  title: "Join TractionX - Accept Invitation",
  description: "Complete your TractionX account setup and join the future of intelligent investing.",
}

export default function AcceptInvitePage() {
  return (
    <AuthLayout
      title="You're invited!"
      subtitle="Join TractionX and unlock the power of AI-driven investing"
    >
      <Suspense fallback={
        <div className="flex items-center justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      }>
        <AcceptInviteForm />
      </Suspense>
    </AuthLayout>
  )
}
