// Deal types and interfaces for the frontend

// Enhanced exclusion filter result structure
export interface ExclusionFilterResult {
  excluded: boolean;
  filter_id?: string;
  filter_name?: string;
  reason?: string;
}

// Instance-level scoring for repeatable sections
export interface InstanceScore {
  score: number;
  matched: boolean;
  value: any;
  explanation: string;
}

// Question-level scoring details
export interface QuestionScore {
  rule_id: string;
  question_id: string;
  question_type: string;
  question_label: string;
  raw_score: number;
  weight: number;
  weighted_score: number;
  explanation: string;
  sources: string[];
  ai_generated: boolean;
  aggregation_used?: boolean;
  aggregation_type?: string;
  is_repeatable?: boolean;
  instances?: InstanceScore[];
}

// Bonus/penalty rule scoring
export interface BonusScore {
  rule_id: string;
  rule_name?: string;
  rule_type: 'BONUS' | 'PENALTY';
  points: number;
  status: 'awarded' | 'blocked' | 'failed';
  reason: string;
  explanation?: string;
}

// Comprehensive thesis scoring structure
export interface ThesisScoring {
  total_score: number;
  normalized_score: number;
  core_score: number;
  bonus_total: number;
  penalty_total?: number;
  max_possible_score: number;
  question_scores: Record<string, QuestionScore>;
  bonus_scores: Record<string, BonusScore>;
  scoring_details: any[];
  thesis_matches: string[];
  best_thesis_id?: string;
  matching_count: number;
}

// Comprehensive scoring structure from backend
export interface ComprehensiveScoring {
  thesis: ThesisScoring;
  founders: {
    total_score: number;
    normalized_score: number;
    ai_analysis: string;
    key_insights: string[];
  };
  market: {
    total_score: number;
    normalized_score: number;
    ai_analysis: string;
    key_insights: string[];
  };
  metadata: {
    scoring_version: string;
    scored_at: number;
    total_rules_processed: number;
    ai_scoring_used: boolean;
  };
}

export interface Deal {
  id: string;
  org_id: string;
  form_id: string;
  submission_ids: string[];

  // Core fields
  company_name?: string;
  stage?: string;
  sector?: string | string[];
  company_website?: string;

  // Deal tracking fields
  status: DealStatus;
  exclusion_filter_result?: ExclusionFilterResult;

  // Enhanced scoring structure
  scoring?: ComprehensiveScoring;

  // Enrichment fields
  enrichment_status?: string;
  enriched_data?: Record<string, any>;

  // Additional fields
  notes?: string;
  tags?: string[];
  timeline?: TimelineEvent[];

  // Metadata
  created_by?: string;
  created_at: number;
  updated_at: number;
}

export enum DealStatus {
  NEW = "new",
  TRIAGE = "triage",
  REVIEWED = "reviewed",
  EXCLUDED = "excluded",
  REJECTED = "rejected",
  APPROVED = "approved",
  NEGOTIATING = "negotiating",
  CLOSED = "closed"
}

export interface TimelineEvent {
  date: string;
  event: string;
  notes?: string;
}

export interface DealListResponse {
  deals: Deal[];
  total: number;
  skip: number;
  limit: number;
  has_more: boolean;
}

export interface DealCreateRequest {
  org_id: string;
  form_id: string;
  submission_id: string;
  company_name?: string;
  stage?: string;
  sector?: string | string[];
  status?: DealStatus;
  notes?: string;
  tags?: string[];
}

export interface DealUpdateRequest {
  company_name?: string;
  stage?: string;
  sector?: string | string[];
  status?: DealStatus;
  notes?: string;
  tags?: string[];
}

// UI-specific types for enhanced deal display
export interface DealCardDisplayData {
  id: string;
  company_name: string;
  stage: string;
  sector: string;
  description: string;
  source: string;
  country: string;
  status: DealStatus;
  avatar_color: string;
  initials: string;

  // Enhanced scoring display
  thesis_match_percent?: number;
  core_score?: number;
  bonus_points?: number;
  penalty_points?: number;
  is_excluded?: boolean;
  exclusion_reason?: string;
  last_scored?: number;
}

export interface DealFilters {
  status?: DealStatus;
  stage?: string;
  sector?: string;
  search?: string;
  tags?: string[];
}

// Score breakdown for detailed analysis
export interface ScoreBreakdown {
  overall_score: number;
  thesis_match_percent: number;
  core_score: number;
  max_possible_score: number;
  bonus_total: number;
  penalty_total: number;
  final_score: number;

  // Question-level breakdown
  question_breakdown: QuestionScore[];
  bonus_breakdown: BonusScore[];

  // Exclusion info
  exclusion_info?: ExclusionFilterResult;

  // Metadata
  last_updated: number;
  scoring_version: string;
  ai_scoring_used: boolean;
}

// Instance display for repeatable sections
export interface InstanceDisplay {
  instance_id: string;
  instance_label: string; // "Founder 1", "Founder 2", etc.
  value: any;
  score: number;
  matched: boolean;
  explanation: string;
}

// Question display with instances
export interface QuestionDisplay extends QuestionScore {
  instances_display?: InstanceDisplay[];
  color_class: string;
  icon_class: string;
  status_text: string;
}

// Mock data types for demo
export interface MockDealData extends DealCardDisplayData {
  created_at: number;
  updated_at: number;
}
