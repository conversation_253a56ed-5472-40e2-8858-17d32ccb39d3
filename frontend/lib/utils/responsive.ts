/**
 * Mobile-First Responsive Utilities for TractionX
 * 
 * This file contains utility functions and constants for implementing
 * mobile-first, Notion-grade responsive design patterns.
 */

import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

// PRD-compliant breakpoints
export const BREAKPOINTS = {
  xs: 480,   // Mobile small
  sm: 640,   // Mobile large  
  md: 1024,  // Tablet
  lg: 1440,  // Desktop
  xl: 1920,  // Large desktop
} as const

// Mobile-first responsive class generators
export const responsive = {
  // Grid utilities
  grid: {
    mobile: "grid grid-cols-1 gap-4",
    tablet: "xs:grid-cols-2 md:gap-6",
    desktop: "md:grid-cols-3 lg:gap-8",
    dashboard: "grid grid-cols-1 xs:grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 lg:gap-8",
  },
  
  // Padding utilities
  padding: {
    mobile: "p-4",
    tablet: "md:p-6", 
    desktop: "lg:p-8",
    section: "px-4 py-6 md:px-6 md:py-8 lg:px-8 lg:py-10",
    card: "p-4 md:p-6 lg:p-8",
  },
  
  // Typography utilities
  text: {
    heading: "text-2xl md:text-3xl lg:text-4xl",
    subheading: "text-xl md:text-2xl lg:text-3xl",
    body: "text-base md:text-lg",
    small: "text-sm md:text-base",
    caption: "text-xs md:text-sm",
  },
  
  // Layout utilities
  layout: {
    container: "w-full max-w-none px-4 md:px-6 lg:px-8",
    sidebar: "w-full max-w-sm md:max-w-md lg:max-w-lg",
    modal: "w-full max-w-md md:max-w-lg lg:max-w-xl",
  },
  
  // Touch targets
  touch: {
    button: "h-12 min-w-[120px] touch-target",
    icon: "h-12 w-12 touch-target",
    input: "h-12 touch-target",
  },
} as const

// Mobile-first component class generators
export function mobileFirst(classes: {
  mobile: string
  tablet?: string
  desktop?: string
  large?: string
}): string {
  return clsx(
    classes.mobile,
    classes.tablet && `md:${classes.tablet}`,
    classes.desktop && `lg:${classes.desktop}`,
    classes.large && `xl:${classes.large}`
  )
}

// Responsive grid generator
export function responsiveGrid(
  cols: { mobile: number; tablet?: number; desktop?: number; large?: number },
  gap: { mobile: number; tablet?: number; desktop?: number } = { mobile: 4 }
): string {
  const gridCols = {
    1: "grid-cols-1",
    2: "grid-cols-2", 
    3: "grid-cols-3",
    4: "grid-cols-4",
    5: "grid-cols-5",
    6: "grid-cols-6",
  } as const
  
  const gridGaps = {
    2: "gap-2",
    3: "gap-3",
    4: "gap-4",
    6: "gap-6",
    8: "gap-8",
  } as const
  
  return clsx(
    "grid",
    gridCols[cols.mobile as keyof typeof gridCols],
    cols.tablet && `xs:${gridCols[cols.tablet as keyof typeof gridCols]}`,
    cols.desktop && `md:${gridCols[cols.desktop as keyof typeof gridCols]}`,
    cols.large && `lg:${gridCols[cols.large as keyof typeof gridCols]}`,
    gridGaps[gap.mobile as keyof typeof gridGaps],
    gap.tablet && `md:${gridGaps[gap.tablet as keyof typeof gridGaps]}`,
    gap.desktop && `lg:${gridGaps[gap.desktop as keyof typeof gridGaps]}`
  )
}

// Mobile-first spacing generator
export function responsiveSpacing(
  type: "p" | "px" | "py" | "pt" | "pb" | "pl" | "pr" | "m" | "mx" | "my" | "mt" | "mb" | "ml" | "mr",
  sizes: { mobile: number; tablet?: number; desktop?: number }
): string {
  const sizeMap = {
    0: "0", 1: "1", 2: "2", 3: "3", 4: "4", 5: "5", 6: "6", 8: "8", 10: "10", 12: "12", 16: "16", 20: "20", 24: "24"
  } as const
  
  return clsx(
    `${type}-${sizeMap[sizes.mobile as keyof typeof sizeMap]}`,
    sizes.tablet && `md:${type}-${sizeMap[sizes.tablet as keyof typeof sizeMap]}`,
    sizes.desktop && `lg:${type}-${sizeMap[sizes.desktop as keyof typeof sizeMap]}`
  )
}

// Device detection utilities (client-side only)
export const device = {
  isMobile: () => typeof window !== 'undefined' && window.innerWidth < BREAKPOINTS.md,
  isTablet: () => typeof window !== 'undefined' && window.innerWidth >= BREAKPOINTS.md && window.innerWidth < BREAKPOINTS.lg,
  isDesktop: () => typeof window !== 'undefined' && window.innerWidth >= BREAKPOINTS.lg,
  
  // Touch device detection
  isTouch: () => typeof window !== 'undefined' && ('ontouchstart' in window || navigator.maxTouchPoints > 0),
  
  // Safe area detection
  hasSafeArea: () => typeof window !== 'undefined' && CSS.supports('padding-top: env(safe-area-inset-top)'),
}

// Mobile-first animation variants for Framer Motion
export const mobileAnimations = {
  // Slide animations optimized for mobile
  slideUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
    transition: { duration: 0.3, ease: "easeOut" }
  },
  
  slideLeft: {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -20 },
    transition: { duration: 0.3, ease: "easeOut" }
  },
  
  // Scale animations for touch feedback
  touchScale: {
    whileTap: { scale: 0.98 },
    transition: { duration: 0.1 }
  },
  
  // Stagger animations for lists
  stagger: {
    container: {
      animate: {
        transition: {
          staggerChildren: 0.1
        }
      }
    },
    item: {
      initial: { opacity: 0, y: 20 },
      animate: { opacity: 1, y: 0 },
      transition: { duration: 0.3 }
    }
  }
}

// Mobile-first form utilities
export const mobileForm = {
  input: "h-12 w-full rounded-xl border border-input bg-transparent px-4 py-3 text-base touch-target",
  button: "h-12 w-full rounded-xl font-bold transition-all duration-200 touch-target active:scale-[0.98]",
  label: "text-sm font-medium mb-2 block",
  error: "text-sm text-destructive mt-1",
  fieldset: "space-y-4 md:space-y-6",
}

// Mobile-first modal/dialog utilities
export const mobileModal = {
  overlay: "fixed inset-0 z-50 bg-background/80 backdrop-blur-sm",
  content: [
    "fixed z-50 grid w-full gap-4 border bg-background shadow-lg",
    // Mobile: bottom sheet
    "bottom-0 rounded-t-2xl p-6 max-h-[90vh] overflow-y-auto safe-bottom",
    // Tablet+: centered modal
    "xs:bottom-auto xs:top-1/2 xs:left-1/2 xs:-translate-x-1/2 xs:-translate-y-1/2",
    "xs:max-w-lg xs:rounded-2xl xs:max-h-[85vh]"
  ].join(" "),
  close: "absolute right-4 top-4 rounded-xl p-2 opacity-70 hover:opacity-100 hover:bg-accent touch-target",
}

// Mobile-first navigation utilities
export const mobileNav = {
  hamburger: "md:hidden rounded-xl p-2 hover:bg-accent active:scale-95 touch-target",
  overlay: "fixed inset-0 z-50 bg-background/80 backdrop-blur-sm md:hidden",
  panel: [
    "fixed inset-y-0 left-0 w-full max-w-sm bg-background border-r shadow-xl",
    "safe-top safe-bottom"
  ].join(" "),
  item: [
    "flex w-full items-center rounded-xl p-4 text-base font-medium",
    "transition-all duration-200 touch-target",
    "hover:bg-accent hover:text-accent-foreground active:scale-[0.98]"
  ].join(" "),
}

// Enhanced mobile-first patterns for visual retreat
// export const visualRetreat = {
//   // Card patterns with glassmorphism
//   card: {
//     base: [
//       "bg-white/98 backdrop-blur-sm border border-gray-200/50",
//       "rounded-2xl shadow-sm hover:shadow-lg",
//       "transition-all duration-300 ease-out",
//       "touch-manipulation"
//     ].join(" "),
//     interactive: [
//       "cursor-pointer hover:scale-[1.02] active:scale-[0.98]",
//       "hover:bg-white hover:shadow-xl hover:border-gray-300/50"
//     ].join(" "),
//     floating: [
//       "shadow-lg hover:shadow-xl",
//       "border-gray-100 bg-white/95"
//     ].join(" ")
//   },

//   // Tab patterns with animated indicators
//   tabs: {
//     container: "flex overflow-x-auto scrollbar-hide gap-2 p-1 bg-gray-50/50 rounded-xl",
//     item: [
//       "flex-shrink-0 px-4 py-2 rounded-lg text-sm font-medium",
//       "transition-all duration-200 touch-target min-w-[80px]",
//       "hover:bg-white/80 active:scale-95"
//     ].join(" "),
//     active: "bg-white shadow-sm text-gray-900 border border-gray-200/50",
//     inactive: "text-gray-600 hover:text-gray-900"
//   },

//   // Form patterns optimized for mobile
//   form: {
//     container: "space-y-6 p-4 md:p-6 lg:p-8",
//     field: "space-y-2",
//     input: [
//       "w-full h-12 px-4 rounded-xl border border-gray-200",
//       "focus:border-gray-400 focus:ring-2 focus:ring-gray-100",
//       "transition-all duration-200 touch-target",
//       "text-base" // Prevent zoom on iOS
//     ].join(" "),
//     button: [
//       "w-full h-12 rounded-xl font-semibold",
//       "transition-all duration-200 touch-target",
//       "active:scale-[0.98]"
//     ].join(" ")
//   },

//   // List patterns that work beautifully on mobile
//   list: {
//     container: "space-y-3 p-4 md:p-6",
//     item: [
//       "flex items-center justify-between p-4 rounded-xl",
//       "bg-white border border-gray-200/50 shadow-sm",
//       "hover:shadow-md hover:border-gray-300/50",
//       "transition-all duration-200 touch-target"
//     ].join(" "),
//     content: "flex-1 min-w-0 pr-4",
//     action: "flex-shrink-0"
//   },

//   // Modal patterns with bulletproof device-agnostic positioning
//   modal: {
//     overlay: "fixed inset-0 z-50 bg-black/50 backdrop-blur-sm transition-all duration-300",
//     content: [
//       // Mobile: bottom sheet with proper safe areas
//       "fixed bottom-0 left-0 right-0 z-50",
//       "bg-white rounded-t-2xl shadow-xl",
//       "max-h-[90vh] overflow-y-auto",
//       "mx-4 mb-4", // Add margins on mobile for better UX
//       "safe-bottom",
//       // Desktop: perfectly centered modal
//       "md:fixed md:top-1/2 md:left-1/2 md:-translate-x-1/2 md:-translate-y-1/2",
//       "md:bottom-auto md:right-auto md:mx-0 md:mb-0",
//       "md:max-w-lg md:w-full md:rounded-2xl",
//       "md:max-h-[85vh] md:min-h-0"
//     ].join(" "),
//     contentLarge: [
//       // Mobile: bottom sheet with proper safe areas
//       "fixed bottom-0 left-0 right-0 z-50",
//       "bg-white rounded-t-2xl shadow-xl",
//       "max-h-[90vh] overflow-y-auto",
//       "mx-4 mb-4",
//       "safe-bottom",
//       // Desktop: larger centered modal for complex forms
//       "md:fixed md:top-1/2 md:left-1/2 md:-translate-x-1/2 md:-translate-y-1/2",
//       "md:bottom-auto md:right-auto md:mx-0 md:mb-0",
//       "md:max-w-2xl md:w-full md:rounded-2xl",
//       "md:max-h-[90vh] md:min-h-0"
//     ].join(" "),
//     header: "flex items-center justify-between p-6 border-b border-gray-100 flex-shrink-0",
//     body: "p-6 space-y-4 flex-1 overflow-y-auto",
//     footer: "p-6 border-t border-gray-100 flex-shrink-0 space-y-3"
//   },

//   // Floating element patterns with device-aware positioning
//   floating: {
//     // Orbit AI and similar floating elements
//     container: [
//       "fixed z-50 select-none",
//       // Mobile: bottom center, never overlaps nav
//       "bottom-6 left-1/2 -translate-x-1/2",
//       // Desktop: bottom right, proper spacing
//       "md:bottom-6 md:right-6 md:left-auto md:translate-x-0"
//     ].join(" "),
//     // Draggable state (desktop only)
//     draggable: "md:cursor-grab md:active:cursor-grabbing",
//     // Ensure never goes off-screen
//     constrained: "max-w-[calc(100vw-2rem)] max-h-[calc(100vh-2rem)]"
//   }
// } as const

// visualRetreat.ts

export const visualRetreat = {
  // Card patterns with glassmorphism
  card: {
    base: [
      "bg-white/98 backdrop-blur-sm border border-gray-200/50",
      "rounded-2xl shadow-sm hover:shadow-lg",
      "transition-all duration-300 ease-out",
      "touch-manipulation"
    ].join(" "),
    interactive: [
      "cursor-pointer hover:scale-[1.02] active:scale-[0.98]",
      "hover:bg-white hover:shadow-xl hover:border-gray-300/50"
    ].join(" "),
    floating: [
      "shadow-lg hover:shadow-xl",
      "border-gray-100 bg-white/95"
    ].join(" ")
  },

  // Tab patterns with animated indicators
  tabs: {
    container: "flex overflow-x-auto scrollbar-hide gap-2 p-1 bg-gray-50/50 rounded-xl",
    item: [
      "flex-shrink-0 px-4 py-2 rounded-lg text-sm font-medium",
      "transition-all duration-200 touch-target min-w-[80px]",
      "hover:bg-white/80 active:scale-95"
    ].join(" "),
    active: "bg-white shadow-sm text-gray-900 border border-gray-200/50",
    inactive: "text-gray-600 hover:text-gray-900"
  },

  // Form patterns optimized for mobile
  form: {
    container: "space-y-6 p-4 md:p-6 lg:p-8",
    field: "space-y-2",
    input: [
      "w-full h-12 px-4 rounded-xl border border-gray-200",
      "focus:border-gray-400 focus:ring-2 focus:ring-gray-100",
      "transition-all duration-200 touch-target",
      "text-base" // Prevent zoom on iOS
    ].join(" "),
    button: [
      "w-full h-12 px-6 rounded-xl font-semibold",
      "transition-all duration-200 touch-target",
      "active:scale-[0.98]"
    ].join(" "),
    label: "text-sm md:text-base font-medium mb-1 block",
    error: "text-sm text-destructive mt-1",
    fieldset: "space-y-4"
  },

  // List patterns that work beautifully on mobile
  list: {
    container: "space-y-3 p-4 md:p-6",
    item: [
      "flex items-center justify-between p-4 rounded-xl",
      "bg-white border border-gray-200/50 shadow-sm",
      "hover:shadow-md hover:border-gray-300/50",
      "transition-all duration-200 touch-target"
    ].join(" "),
    content: "flex-1 min-w-0 pr-4",
    action: "flex-shrink-0"
  },

  // Premium Modal System - Notion/Linear/Airtable Grade
  modal: {
    // Glassmorphism overlay - perfect backdrop
    overlay: [
      "fixed inset-0 z-50",
      "bg-black/50 backdrop-blur-sm",
      "data-[state=closed]:animate-out data-[state=closed]:fade-out-0",
      "data-[state=open]:animate-in data-[state=open]:fade-in-0",
      "transition-all duration-300 ease-out"
    ].join(" "),

    // Standard modal (max-w-md ~480px) - Always perfectly centered
    content: [
      "fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50",
      "w-[90vw] max-w-md min-w-[320px]",
      "max-h-[80vh]",
      "bg-white/90 backdrop-blur-lg",
      "rounded-2xl shadow-xl",
      "p-8",
      // Mobile adjustments
      "xs:p-6",
      // Animations
      "data-[state=open]:animate-in data-[state=open]:fade-in-0 data-[state=open]:zoom-in-95",
      "data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95",
      "transition-all duration-300 ease-out"
    ].join(" "),

    // Large modal (max-w-lg ~512px) for complex forms
    contentLarge: [
      "fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50",
      "w-[90vw] max-w-lg min-w-[320px]",
      "max-h-[80vh]",
      "bg-white/90 backdrop-blur-lg",
      "rounded-2xl shadow-xl",
      "p-8",
      // Mobile adjustments
      "xs:p-6",
      // Animations
      "data-[state=open]:animate-in data-[state=open]:fade-in-0 data-[state=open]:zoom-in-95",
      "data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95",
      "transition-all duration-300 ease-out"
    ].join(" "),

    // Extra large modal (max-w-xl ~576px) for very complex forms
    contentXLarge: [
      "fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50",
      "w-[90vw] max-w-xl min-w-[320px]",
      "max-h-[80vh]",
      "bg-white/90 backdrop-blur-lg",
      "rounded-2xl shadow-xl",
      "p-8",
      // Mobile adjustments
      "xs:p-6",
      // Animations
      "data-[state=open]:animate-in data-[state=open]:fade-in-0 data-[state=open]:zoom-in-95",
      "data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95",
      "transition-all duration-300 ease-out"
    ].join(" "),

    // Alert/Confirmation modal (max-w-xs ~320px) - Compact
    contentAlert: [
      "fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50",
      "w-[90vw] max-w-xs min-w-[280px]",
      "max-h-[80vh]",
      "bg-white/90 backdrop-blur-lg",
      "rounded-2xl shadow-xl",
      "p-6",
      // Mobile adjustments
      "xs:p-5",
      // Animations
      "data-[state=open]:animate-in data-[state=open]:fade-in-0 data-[state=open]:zoom-in-95",
      "data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95",
      "transition-all duration-300 ease-out"
    ].join(" "),

    // Content areas - no backgrounds, clean spacing
    header: "mb-6",
    body: "space-y-4 max-h-[80vh] overflow-y-auto",

    // Button areas - floating at bottom, no backgrounds
    actions: [
      "flex gap-3 pt-6 mt-6",
      // Desktop: side-by-side alignment
      "justify-end",
      // Mobile: stack vertically below 480px
      "max-xs:flex-col max-xs:gap-2"
    ].join(" "),

    // Close button - minimal and accessible
    closeButton: [
      "absolute top-4 right-4 z-10",
      "p-2 rounded-xl",
      "opacity-70 hover:opacity-100",
      "hover:bg-gray-100/80",
      "focus:outline-none focus:ring-2 focus:ring-gray-200 focus:ring-offset-2",
      "transition-all duration-200",
      "touch-target"
    ].join(" ")
  },

  // Floating element patterns with device-aware positioning
  floating: {
    // Orbit AI and similar floating elements
    container: [
      "fixed z-50 select-none",
      // Mobile: bottom center, never overlaps nav
      "bottom-6 left-1/2 -translate-x-1/2",
      // Desktop: bottom right, proper spacing
      "md:bottom-6 md:right-6 md:left-auto md:translate-x-0"
    ].join(" "),
    // Draggable state (desktop only)
    draggable: "md:cursor-grab md:active:cursor-grabbing",
    // Ensure never goes off-screen
    constrained: "max-w-[calc(100vw-2rem)] max-h-[calc(100vh-2rem)]"
  }
} as const


// Export individual pattern functions for easier usage
export const getVisualRetreatCard = (variant: 'base' | 'interactive' | 'floating' = 'base') => {
  const base = visualRetreat.card.base
  switch (variant) {
    case 'interactive':
      return `${base} ${visualRetreat.card.interactive}`
    case 'floating':
      return `${base} ${visualRetreat.card.floating}`
    default:
      return base
  }
}

export const getVisualRetreatTab = (isActive: boolean) => {
  return `${visualRetreat.tabs.item} ${isActive ? visualRetreat.tabs.active : visualRetreat.tabs.inactive}`
}

export const getVisualRetreatModal = (section: 'overlay' | 'content' | 'header' | 'body' | 'footer') => {
  return visualRetreat.modal[section]
}

export const getVisualRetreatForm = (element: 'container' | 'field' | 'input' | 'button') => {
  return visualRetreat.form[element]
}

export const getVisualRetreatList = (element: 'container' | 'item' | 'content' | 'action') => {
  return visualRetreat.list[element]
}

// Complete visual retreat system for production-ready mobile-first design
export const mobileRetreat = {
  // Page layouts
  page: {
    container: "min-h-screen bg-gray-50 flex flex-col",
    content: "flex-1 px-4 py-6 md:px-6 md:py-8 lg:px-8 lg:py-10 safe-top safe-bottom",
    maxWidth: "max-w-7xl mx-auto w-full"
  },

  // Header patterns
  header: {
    sticky: "sticky top-0 z-40 bg-white/95 backdrop-blur-sm border-b border-gray-200/50 safe-top",
    content: "flex items-center justify-between h-16 px-4 md:px-6 lg:px-8",
    title: "text-2xl md:text-3xl font-bold text-gray-900",
    subtitle: "text-sm md:text-base text-gray-600 mt-1"
  },

  // Grid systems
  grid: {
    responsive: "grid grid-cols-1 xs:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6 lg:gap-8",
    cards: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8",
    stats: "grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6"
  },

  // Loading states
  loading: {
    container: "flex items-center justify-center p-8 md:p-12",
    spinner: "animate-spin rounded-full h-8 w-8 md:h-12 md:w-12 border-2 border-gray-300 border-t-gray-900",
    text: "text-sm md:text-base text-gray-600 mt-4"
  },

  // Empty states
  empty: {
    container: "text-center py-12 md:py-16 lg:py-20",
    icon: "w-16 h-16 md:w-20 md:h-20 mx-auto mb-4 text-gray-400",
    title: "text-lg md:text-xl font-semibold text-gray-900 mb-2",
    description: "text-sm md:text-base text-gray-600 mb-6",
    action: "inline-flex items-center justify-center"
  },

  // Error states
  error: {
    container: "text-center py-8 md:py-12",
    icon: "w-12 h-12 md:w-16 md:h-16 mx-auto mb-4 text-red-500",
    title: "text-lg md:text-xl font-semibold text-gray-900 mb-2",
    description: "text-sm md:text-base text-gray-600 mb-4"
  }
} as const
