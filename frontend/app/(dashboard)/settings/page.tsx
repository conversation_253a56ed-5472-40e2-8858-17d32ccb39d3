"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { visualRetreat, mobileRetreat } from "@/lib/utils/responsive"



export default function SettingsPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to profile tab by default
    router.replace('/settings/profile')
  }, [router])

  return (
    <div className="flex items-center justify-center min-h-screen safe-top safe-bottom">
      <div className="text-center">
        <p className="text-muted-foreground">Redirecting to settings...</p>
      </div>
    </div>
  )
}


