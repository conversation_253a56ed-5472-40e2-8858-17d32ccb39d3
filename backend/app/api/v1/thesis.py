"""
Investment Thesis API Routes

This module defines the API routes for managing investment theses, including
thesis creation, updating, and scoring.
"""

from typing import Any, Dict, List, Optional, Tuple, Union

from fastapi import Body, Depends, HTTPException, Path, Query, status

from app.api.base import BaseAPIRouter
from app.core.logging import get_logger
from app.dependencies.auth import get_current_user
from app.dependencies.org import get_org_context
from app.models.thesis import (
    AggregationType,
    InvestmentThesis,
    MatchRule,
    RuleType,
    ScoringRule,
    ThesisStatus,
    ThesisWithRules,
)
from app.services.factory import get_thesis_service
from app.services.thesis.interfaces import ThesisServiceInterface
from app.utils.thesis.migration import migrate_thesis_rules

router = BaseAPIRouter(
    prefix="/thesis",
    tags=["thesis"],
    responses={
        404: {"description": "Not found"},
        403: {"description": "Forbidden"},
        400: {"description": "Bad request"},
        500: {"description": "Internal server error"},
    },
)

logger = get_logger(__name__)


@router.post(
    "/", response_model=ThesisWithRules, description="Create a new investment thesis"
)
async def create_thesis(
    thesis_data: Dict[str, Any],
    current_user: Dict = Depends(get_current_user),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
) -> ThesisWithRules:
    """
    Create a new investment thesis with optional initial scoring and match rules.

    The request can include:
    - Basic thesis information (name, description, form_id)
    - Initial scoring_rules list
    - Initial match_rules list

    Args:
        thesis_data: Dictionary containing thesis data and optional rules
        current_user: Current user information
        org_context: Tuple of (organization_id, is_cross_org)
        thesis_service: Thesis service instance

    Returns:
        Created thesis with all rules
    """
    # Validate required fields
    if not thesis_data.get("name"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Thesis name is required"
        )
    if not thesis_data.get("form_id"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Form ID is required"
        )

    # Extract initial rules if provided
    initial_scoring_rules = thesis_data.pop("scoring_rules", [])
    initial_match_rules = thesis_data.pop("match_rules", [])

    try:
        # Create thesis
        thesis = await thesis_service.create_thesis(
            name=thesis_data["name"],
            description=thesis_data.get("description", ""),
            form_id=thesis_data["form_id"],
            org_id=org_context[0],
            created_by=current_user.id,
            status=thesis_data.get("status", ThesisStatus.DRAFT),
            is_active=thesis_data.get("is_active", True),
        )

        if initial_scoring_rules:
            # Create rules using service methods
            created_scoring_rules = await thesis_service.create_thesis_scoring_rules(
                thesis.id, initial_scoring_rules
            )
            # Update the thesis object with the rule IDs
            thesis.scoring_rules = [rule.id for rule in created_scoring_rules]
        if initial_match_rules:
            created_match_rules = await thesis_service.create_thesis_match_rules(
                thesis.id, initial_match_rules
            )

            thesis.match_rules = [rule.id for rule in created_match_rules]

        # Save the updated thesis with the rule IDs
        await thesis.save(is_update=True)

        # Fetch the thesis with rules from the database to ensure consistency
        thesis_with_rules = await thesis_service.get_thesis_with_rules(thesis.id)

        return thesis_with_rules
    except Exception as e:
        # Log the error and return a 500 response
        logger.error(f"Error creating thesis: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create thesis: {str(e)}",
        )


@router.get("/{thesis_id}", response_model=ThesisWithRules)
async def get_thesis(
    thesis_id: str = Path(..., description="Thesis ID"),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
) -> ThesisWithRules:
    """
    Get a thesis by ID with all its rules.

    Args:
        thesis_id: Thesis ID
        org_context: Tuple of (organization_id, is_cross_org)
        thesis_service: Thesis service instance

    Returns:
        Thesis with rules
    """
    try:
        # Validate thesis ownership
        try:
            await thesis_service.validate_thesis_ownership(thesis_id, org_context[0])
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND
                if "not found" in str(e)
                else status.HTTP_403_FORBIDDEN,
                detail=str(e),
            )

        # Get thesis with rules
        thesis: ThesisWithRules = await thesis_service.get_thesis_with_rules(thesis_id)  # type: ignore
        if not thesis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Thesis not found"
            )

        return thesis.model_dump(by_alias=True)
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error retrieving thesis: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve thesis: {str(e)}",
        )


async def update_scoring_rules_for_thesis(
    thesis_id: str,
    scoring_rules: List[Dict[str, Any]],
    thesis_service: ThesisServiceInterface,
) -> None:
    """
    Update scoring rules for a thesis.

    Args:
        thesis_id: ID of the thesis to update rules for
        scoring_rules: List of rule data dictionaries
        thesis_service: Thesis service instance
    """
    # Get existing rules
    existing_rules = await thesis_service.list_scoring_rules(thesis_id)
    existing_rule_map = {str(rule.id): rule for rule in existing_rules}

    # Process each rule
    for rule_data in scoring_rules:
        rule_id = rule_data.get("id")

        if rule_id and rule_id in existing_rule_map:
            # Update existing rule
            await thesis_service.update_scoring_rule(rule_id, rule_data)
        elif (
            rule_data.get("question_id") or rule_data.get("rule_type") == "bonus"
        ):  # Allow bonus rules without question_id
            # Create new rule
            data = rule_data.copy()
            if "thesis_id" in data:
                del data["thesis_id"]
            await thesis_service.create_scoring_rule(
                thesis_id,
                **data,  # Pass all rule data to the service
            )

    # Remove rules not in the update
    if scoring_rules:
        update_rule_ids = {rule.get("id") for rule in scoring_rules if rule.get("id")}
        for rule_id in existing_rule_map.keys():
            if rule_id not in update_rule_ids:
                await thesis_service.delete_scoring_rule(rule_id)


async def update_match_rules_for_thesis(
    thesis_id: str,
    match_rules: List[Dict[str, Any]],
    thesis_service: ThesisServiceInterface,
) -> None:
    """
    Update match rules for a thesis.

    Args:
        thesis_id: ID of the thesis to update rules for
        match_rules: List of rule data dictionaries
        thesis_service: Thesis service instance
    """
    # Get existing rules
    existing_rules = await thesis_service.list_match_rules(thesis_id)
    existing_rule_map = {str(rule.id): rule for rule in existing_rules}

    # Process each rule
    for rule_data in match_rules:
        rule_id = rule_data.get("id")

        if rule_id and rule_id in existing_rule_map:
            # Update existing rule
            await thesis_service.update_match_rule(rule_id, rule_data)
        elif rule_data.get("name"):  # Ensure name is present for new rules
            # Create new rule
            await thesis_service.create_match_rule(
                thesis_id,
                name=rule_data["name"],
                description=rule_data.get("description"),
                operator=rule_data.get("operator", "and"),
                conditions=rule_data.get("conditions", []),
            )

    # Remove rules not in the update
    if match_rules:
        update_rule_ids = {rule.get("id") for rule in match_rules if rule.get("id")}
        for rule_id in existing_rule_map.keys():
            if rule_id not in update_rule_ids:
                await thesis_service.delete_match_rule(rule_id)


@router.put("/{thesis_id}", response_model=ThesisWithRules)
async def update_thesis(
    thesis_data: Dict[str, Any],
    thesis_id: str = Path(..., description="Thesis ID"),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
) -> Dict[str, Any]:
    """
    Update an existing thesis with optional rule updates.

    The request can include:
    - Basic thesis information (name, description, status, is_active)
    - scoring_rules list to update/replace existing rules
    - match_rules list to update/replace existing rules

    Args:
        thesis_data: Dictionary containing thesis data and optional rules to update
        thesis_id: Thesis ID
        org_context: Tuple of (organization_id, is_cross_org)
        thesis_service: Thesis service instance

    Returns:
        Updated thesis with all rules
    """
    try:
        # Validate thesis ownership
        try:
            await thesis_service.validate_thesis_ownership(thesis_id, org_context[0])
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND
                if "not found" in str(e)
                else status.HTTP_403_FORBIDDEN,
                detail=str(e),
            )

        # Extract rule updates if provided
        scoring_rules = thesis_data.pop("scoring_rules", None)
        match_rules = thesis_data.pop("match_rules", None)

        # Update thesis basic info
        updated_thesis = await thesis_service.update_thesis(thesis_id, thesis_data)
        logger.info(f"Updated thesis: {updated_thesis}")
        if not updated_thesis:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update thesis",
            )

        # Update rules if provided
        if scoring_rules is not None:
            await thesis_service.update_thesis_scoring_rules(thesis_id, scoring_rules)
            logger.info(f"Updated scoring rules: {scoring_rules}")

        if match_rules is not None:
            await thesis_service.update_thesis_match_rules(thesis_id, match_rules)
            logger.info(f"Updated match rules: {match_rules}")

        # Return thesis with all rules
        updated_thesis = await thesis_service.get_thesis_with_rules(thesis_id)
        if not updated_thesis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Thesis not found"
            )

        return updated_thesis.model_dump(by_alias=True)
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error updating thesis: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update thesis: {str(e)}",
        )


@router.delete("/{thesis_id}", response_model=Dict[str, bool])
async def delete_thesis(
    thesis_id: str = Path(..., description="Thesis ID"),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
) -> Dict[str, bool]:
    """
    Delete a thesis.

    Args:
        thesis_id: Thesis ID
        org_context: Tuple of (organization_id, is_cross_org)
        thesis_service: Thesis service instance

    Returns:
        Success status
    """
    try:
        # Validate thesis ownership
        try:
            await thesis_service.validate_thesis_ownership(thesis_id, org_context[0])
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND
                if "not found" in str(e)
                else status.HTTP_403_FORBIDDEN,
                detail=str(e),
            )

        # Delete thesis and all associated rules
        success = await thesis_service.delete_thesis(thesis_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete thesis",
            )

        return {"success": True}
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error deleting thesis: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete thesis: {str(e)}",
        )


@router.get("", response_model=Union[List[InvestmentThesis], List[Dict[str, Any]]])
async def list_theses(
    form_id: Optional[str] = Query(None, description="Filter by form ID"),
    status: Optional[str] = Query(None, description="Filter by status"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    matching_rules: bool = Query(
        True, description="Include detailed matching rules in response"
    ),
    skip: int = Query(0, description="Number of theses to skip"),
    limit: int = Query(100, description="Maximum number of theses to return"),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
) -> Union[List[InvestmentThesis], List[Dict[str, Any]]]:
    """
    List theses with optional filtering and detailed matching rules.

    Args:
        form_id: Optional filter by form ID
        status: Optional filter by status
        is_active: Optional filter by active status
        matching_rules: Include detailed matching rules in response
        skip: Number of theses to skip
        limit: Maximum number of theses to return
        org_context: Tuple of (organization_id, is_cross_org)
        thesis_service: Thesis service instance

    Returns:
        List of theses matching the filters, with optional detailed match rules
    """
    try:
        theses = await thesis_service.list_theses(
            org_id=org_context[0],
            skip=skip,
            limit=limit,
            form_id=form_id,
            status=status,
            is_active=is_active,
        )
        return theses

        # If matching_rules is True, enhance with detailed match rules
        # if matching_rules:
        #     if not theses:
        #         return []

        #     result = []
        #     for thesis in theses:
        #         # Get detailed match rules
        #         match_rules = await thesis_service.list_match_rules(thesis.id)

        #         # Get scoring rules count only
        #         scoring_rules = await thesis_service.list_scoring_rules(thesis.id)
        #         scoring_rules_count = len(scoring_rules)

        #         # Build enhanced thesis response
        #         thesis_data = thesis.model_dump(by_alias=True)
        #         thesis_data["match_rules"] = [rule.model_dump(by_alias=True) for rule in match_rules]
        #         thesis_data["scoring_rules_count"] = scoring_rules_count

        #         result.append(thesis_data)

        #     return result
        # else:
        #     # Return basic thesis list
        #     return theses

    except Exception as e:
        logger.error(f"Error listing theses: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list theses: {str(e)}",
        )


@router.post("/{thesis_id}/scoring-rules", response_model=ThesisWithRules)
async def create_scoring_rule(
    rule_data: Dict[str, Any] = Body(..., description="Scoring rule data"),
    thesis_id: str = Path(..., description="Thesis ID"),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
) -> ThesisWithRules:
    """
    Create a new scoring rule for a thesis.

    The rule can be either a scoring rule or a bonus rule, with support for:
    - Simple conditions on single questions
    - Compound conditions with AND/OR/NOT logic
    - Aggregation for repeatable sections
    - Bonus points for bonus rules

    Args:
        rule_data: Dictionary containing rule data
        thesis_id: Thesis ID
        org_context: Tuple of (organization_id, is_cross_org)
        thesis_service: Thesis service instance

    Returns:
        Created scoring rule
    """
    try:
        # Validate thesis ownership
        try:
            await thesis_service.validate_thesis_ownership(thesis_id, org_context[0])
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND
                if "not found" in str(e)
                else status.HTTP_403_FORBIDDEN,
                detail=str(e),
            )

        # Validate required fields based on rule type
        rule_type = rule_data.get("rule_type")
        if not rule_type or rule_type not in [rt.value for rt in RuleType]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Valid rule_type (scoring/bonus) is required",
            )

        if rule_type == RuleType.SCORING.value:
            if not rule_data.get("question_id"):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="question_id is required for scoring rules",
                )
            if rule_data.get("bonus_points") is not None:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="bonus_points cannot be set for scoring rules",
                )

        if rule_type == RuleType.BONUS.value:
            if not rule_data.get("bonus_points"):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="bonus_points is required for bonus rules",
                )

        # Validate condition structure
        condition = rule_data.get("condition")
        if not condition:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="condition is required"
            )

        # Validate aggregation fields if present
        if rule_data.get("aggregation"):
            if not rule_data.get("section_id"):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="section_id is required when using aggregation",
                )

            # Validate threshold for aggregation types that require it
            aggregation = rule_data.get("aggregation")
            if aggregation in [
                AggregationType.COUNT.value,
                AggregationType.PERCENTAGE.value,
                AggregationType.SUM.value,
            ]:
                if rule_data.get("aggregate_threshold") is None:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"aggregate_threshold is required for {aggregation} aggregation",
                    )

            # Validate operator for SUM aggregation
            if aggregation == AggregationType.SUM.value:
                if rule_data.get("aggregate_operator") is None:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="aggregate_operator is required for SUM aggregation",
                    )

        # Create rule
        data = rule_data.copy()
        if "thesis_id" in data:
            del data["thesis_id"]
        await thesis_service.create_scoring_rule(thesis_id=thesis_id, **data)
        thesis = await thesis_service.get_thesis_with_rules(thesis_id)
        if not thesis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Thesis not found"
            )

        return thesis.model_dump(by_alias=True)
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating scoring rule: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create scoring rule: {str(e)}",
        )


@router.put("/scoring-rules/{rule_id}", response_model=ThesisWithRules)
async def update_scoring_rule(
    rule_data: Dict[str, Any] = Body(..., description="Updated scoring rule data"),
    rule_id: str = Path(..., description="Rule ID"),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
) -> ThesisWithRules:
    """
    Update an existing scoring rule.

    Args:
        rule_data: Dictionary containing updated rule data
        rule_id: Rule ID
        org_context: Tuple of (organization_id, is_cross_org)
        thesis_service: Thesis service instance

    Returns:
        Updated scoring rule
    """
    try:
        # Validate rule ownership
        try:
            await thesis_service.validate_rule_ownership(
                rule_id, org_context[0], thesis_service.get_scoring_rule, "scoring rule"
            )
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND
                if "not found" in str(e)
                else status.HTTP_403_FORBIDDEN,
                detail=str(e),
            )

        # Get existing rule to validate updates
        existing_rule = await thesis_service.get_scoring_rule(rule_id)
        if not existing_rule:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Scoring rule not found"
            )

        # Validate rule type changes
        if (
            "rule_type" in rule_data
            and rule_data["rule_type"] != existing_rule.rule_type
        ):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot change rule_type of existing rule",
            )

        # Validate condition changes
        if "condition" in rule_data:
            condition = rule_data["condition"]
            if not condition:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="condition is required",
                )

        # Update rule
        updated_rule = await thesis_service.update_scoring_rule(rule_id, rule_data)
        if not updated_rule:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update scoring rule",
            )
        thesis = await thesis_service.get_thesis_with_rules(updated_rule.thesis_id)
        if not thesis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Thesis not found"
            )

        return thesis.model_dump(by_alias=True)
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating scoring rule: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update scoring rule: {str(e)}",
        )


@router.post("/scoring-rules/validate", response_model=Dict[str, Any])
async def validate_scoring_rule(
    rule_data: Dict[str, Any] = Body(..., description="Scoring rule data to validate"),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
) -> Dict[str, Any]:
    """
    Validate a scoring rule without creating it.

    This endpoint performs all validation checks that would be done during
    rule creation, but doesn't actually create the rule.

    Args:
        rule_data: Dictionary containing rule data to validate
        thesis_service: Thesis service instance

    Returns:
        Dictionary with validation results
    """
    try:
        # Validate rule type
        rule_type = rule_data.get("rule_type")
        if not rule_type or rule_type not in [rt.value for rt in RuleType]:
            return {
                "valid": False,
                "errors": ["Valid rule_type (scoring/bonus) is required"],
            }

        errors = []
        warnings = []

        # Validate based on rule type
        if rule_type == RuleType.SCORING.value:
            if not rule_data.get("question_id"):
                errors.append("question_id is required for scoring rules")
            if rule_data.get("bonus_points") is not None:
                errors.append("bonus_points cannot be set for scoring rules")
            if not rule_data.get("weight") or rule_data.get("weight") <= 0:
                errors.append("positive weight is required for scoring rules")

        if rule_type == RuleType.BONUS.value:
            if not rule_data.get("bonus_points"):
                errors.append("bonus_points is required for bonus rules")
            if rule_data.get("weight") != 1.0:
                warnings.append("weight will be set to 1.0 for bonus rules")

        # Validate condition
        condition = rule_data.get("condition")
        if not condition:
            errors.append("condition is required")
        else:
            # Validate condition structure
            if isinstance(condition, dict):
                if "operator" not in condition:
                    errors.append("condition operator is required")
                if "value" not in condition:
                    errors.append("condition value is required")
                if (
                    "question_id" not in condition
                    and rule_type == RuleType.SCORING.value
                ):
                    errors.append(
                        "question_id is required in condition for scoring rules"
                    )

        # Validate aggregation fields
        if rule_data.get("aggregation"):
            if not rule_data.get("section_id"):
                errors.append("section_id is required when using aggregation")

            aggregation = rule_data.get("aggregation")
            if aggregation != AggregationType.NONE.value:
                # Validate threshold for aggregation types that require it
                if aggregation in [
                    AggregationType.COUNT.value,
                    AggregationType.PERCENTAGE.value,
                    AggregationType.SUM.value,
                ]:
                    if rule_data.get("aggregate_threshold") is None:
                        errors.append(
                            f"aggregate_threshold is required for {aggregation} aggregation"
                        )

                # Validate operator for SUM aggregation
                if aggregation == AggregationType.SUM.value:
                    if rule_data.get("aggregate_operator") is None:
                        errors.append(
                            "aggregate_operator is required for SUM aggregation"
                        )

        return {"valid": len(errors) == 0, "errors": errors, "warnings": warnings}
    except Exception as e:
        logger.error(f"Error validating scoring rule: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to validate scoring rule: {str(e)}",
        )


@router.delete("/scoring-rules/{rule_id}", response_model=Dict[str, str])
async def delete_scoring_rule(
    rule_id: str = Path(..., description="Rule ID"),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
) -> Dict[str, str]:
    """
    Delete a scoring rule.

    Args:
        rule_id: Rule ID
        org_context: Tuple of (organization_id, is_cross_org)
        thesis_service: Thesis service instance

    Returns:
        Success message
    """
    try:
        # Validate rule ownership
        try:
            await thesis_service.validate_rule_ownership(
                rule_id, org_context[0], thesis_service.get_scoring_rule, "scoring rule"
            )
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND
                if "not found" in str(e)
                else status.HTTP_403_FORBIDDEN,
                detail=str(e),
            )

        # Delete rule
        success = await thesis_service.delete_scoring_rule(rule_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete scoring rule",
            )

        return {"message": "Scoring rule deleted successfully"}
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error deleting scoring rule: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete scoring rule: {str(e)}",
        )


@router.get("/scoring-rules/{rule_id}", response_model=ScoringRule)
async def get_scoring_rule(
    rule_id: str = Path(..., description="Rule ID"),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
) -> ScoringRule:
    """
    Get a scoring rule by ID.

    Args:
        rule_id: Rule ID
        org_context: Tuple of (organization_id, is_cross_org)
        thesis_service: Thesis service instance

    Returns:
        The scoring rule if found
    """
    try:
        # Validate rule ownership
        try:
            rule = await thesis_service.validate_rule_ownership(
                rule_id, org_context[0], thesis_service.get_scoring_rule, "scoring rule"
            )
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND
                if "not found" in str(e)
                else status.HTTP_403_FORBIDDEN,
                detail=str(e),
            )

        return rule
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error retrieving scoring rule: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve scoring rule: {str(e)}",
        )


@router.get("/{thesis_id}/scoring-rules", response_model=List[ScoringRule])
async def list_scoring_rules(
    thesis_id: str = Path(..., description="Thesis ID"),
    exclude_from_scoring: Optional[bool] = Query(
        None, description="Filter by exclude_from_scoring"
    ),
    rule_type: Optional[str] = Query(None, description="Filter by rule_type"),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
) -> List[ScoringRule]:
    """
    List scoring rules for a thesis with optional filtering.

    Args:
        thesis_id: Thesis ID
        exclude_from_scoring: Optional filter by exclude_from_scoring
        rule_type: Optional filter by rule_type
        org_context: Tuple of (organization_id, is_cross_org)
        thesis_service: Thesis service instance

    Returns:
        List of scoring rules matching the filters
    """
    try:
        # Validate thesis ownership
        try:
            await thesis_service.validate_thesis_ownership(thesis_id, org_context[0])
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND
                if "not found" in str(e)
                else status.HTTP_403_FORBIDDEN,
                detail=str(e),
            )

        # Get rules with filters
        rules = await thesis_service.list_scoring_rules(
            thesis_id=thesis_id,
            exclude_from_scoring=exclude_from_scoring,
            rule_type=rule_type,
        )

        return rules
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error listing scoring rules: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list scoring rules: {str(e)}",
        )


@router.post("/{thesis_id}/match-rules", response_model=ThesisWithRules)
async def create_match_rule(
    rule_data: Dict[str, Any],
    thesis_id: str = Path(..., description="Thesis ID"),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
) -> ThesisWithRules:
    """
    Create a new match rule for a thesis.

    Args:
        rule_data: Dictionary containing rule data
        thesis_id: Thesis ID
        org_context: Tuple of (organization_id, is_cross_org)
        thesis_service: Thesis service instance

    Returns:
        Created match rule
    """
    try:
        # Validate required fields
        if not rule_data.get("name"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Rule name is required"
            )

        # Validate thesis ownership
        try:
            await thesis_service.validate_thesis_ownership(thesis_id, org_context[0])
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND
                if "not found" in str(e)
                else status.HTTP_403_FORBIDDEN,
                detail=str(e),
            )

        # Create rule
        await thesis_service.create_match_rule(
            thesis_id=thesis_id,
            name=rule_data["name"],
            description=rule_data.get("description"),
            operator=rule_data.get("operator", "and"),
            conditions=rule_data.get("conditions", []),
        )
        thesis = await thesis_service.get_thesis_with_rules(thesis_id)
        if not thesis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Thesis not found"
            )
        return thesis.model_dump(by_alias=True)
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error creating match rule: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create match rule: {str(e)}",
        )


@router.put("/match-rules/{rule_id}", response_model=ThesisWithRules)
async def update_match_rule(
    rule_data: Dict[str, Any],
    rule_id: str = Path(..., description="Rule ID"),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
) -> ThesisWithRules:
    """
    Update an existing match rule.

    Args:
        rule_data: Dictionary containing rule data to update
        rule_id: Rule ID
        org_context: Tuple of (organization_id, is_cross_org)
        thesis_service: Thesis service instance

    Returns:
        Updated match rule
    """
    try:
        # Validate rule ownership
        try:
            await thesis_service.validate_rule_ownership(
                rule_id, org_context[0], thesis_service.get_match_rule, "match rule"
            )
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND
                if "not found" in str(e)
                else status.HTTP_403_FORBIDDEN,
                detail=str(e),
            )

        # Update rule
        updated_rule = await thesis_service.update_match_rule(rule_id, rule_data)
        if not updated_rule:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update match rule",
            )
        thesis = await thesis_service.get_thesis_with_rules(updated_rule.thesis_id)
        if not thesis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Thesis not found"
            )
        return thesis.model_dump(by_alias=True)
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error updating match rule: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update match rule: {str(e)}",
        )


@router.delete("/match-rules/{rule_id}", response_model=Dict[str, str])
async def delete_match_rule(
    rule_id: str = Path(..., description="Rule ID"),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
) -> Dict[str, str]:
    """
    Delete a match rule.

    Args:
        rule_id: Rule ID
        org_context: Tuple of (organization_id, is_cross_org)
        thesis_service: Thesis service instance

    Returns:
        Success message
    """
    try:
        # Validate rule ownership
        try:
            await thesis_service.validate_rule_ownership(
                rule_id, org_context[0], thesis_service.get_match_rule, "match rule"
            )
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND
                if "not found" in str(e)
                else status.HTTP_403_FORBIDDEN,
                detail=str(e),
            )

        # Delete rule
        success = await thesis_service.delete_match_rule(rule_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete match rule",
            )

        return {"message": "Match rule deleted successfully"}
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error deleting match rule: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete match rule: {str(e)}",
        )


@router.get("/match-rules/{rule_id}", response_model=MatchRule)
async def get_match_rule(
    rule_id: str = Path(..., description="Rule ID"),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
) -> MatchRule:
    """
    Get a match rule by ID.

    Args:
        rule_id: Rule ID
        org_context: Tuple of (organization_id, is_cross_org)
        thesis_service: Thesis service instance

    Returns:
        The match rule if found
    """
    try:
        # Validate rule ownership
        try:
            rule = await thesis_service.validate_rule_ownership(
                rule_id, org_context[0], thesis_service.get_match_rule, "match rule"
            )
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND
                if "not found" in str(e)
                else status.HTTP_403_FORBIDDEN,
                detail=str(e),
            )

        return rule
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error retrieving match rule: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve match rule: {str(e)}",
        )


@router.get("/{thesis_id}/match-rules", response_model=List[MatchRule])
async def list_match_rules(
    thesis_id: str = Path(..., description="Thesis ID"),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
) -> List[MatchRule]:
    """
    List match rules for a thesis.

    Args:
        thesis_id: Thesis ID
        org_context: Tuple of (organization_id, is_cross_org)
        thesis_service: Thesis service instance

    Returns:
        List of match rules for the thesis
    """
    try:
        # Validate thesis ownership
        try:
            await thesis_service.validate_thesis_ownership(thesis_id, org_context[0])
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND
                if "not found" in str(e)
                else status.HTTP_403_FORBIDDEN,
                detail=str(e),
            )

        # Get rules
        rules = await thesis_service.list_match_rules(thesis_id)

        return rules
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error listing match rules: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list match rules: {str(e)}",
        )


@router.post("/{thesis_id}/calculate-score", response_model=Dict[str, Any])
async def calculate_score(
    form_responses: Dict[str, Any],
    thesis_id: str = Path(..., description="Thesis ID"),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
) -> Dict[str, Any]:
    """
    Calculate score for a form response based on thesis rules.

    Args:
        form_responses: Dictionary of form responses
        thesis_id: Thesis ID
        org_context: Tuple of (organization_id, is_cross_org)
        thesis_service: Thesis service instance

    Returns:
        Dictionary with score details
    """
    try:
        # Validate thesis ownership
        try:
            await thesis_service.validate_thesis_ownership(thesis_id, org_context[0])
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND
                if "not found" in str(e)
                else status.HTTP_403_FORBIDDEN,
                detail=str(e),
            )

        # Calculate score
        score = await thesis_service.calculate_score(thesis_id, form_responses)

        return score
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error calculating score: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to calculate score: {str(e)}",
        )


@router.post("/find-matching", response_model=List[InvestmentThesis])
async def find_matching_theses(
    data: Dict[str, Any],
    org_context: Tuple[str, bool] = Depends(get_org_context),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
) -> List[InvestmentThesis]:
    """
    Find all theses that match a form response based on their match rules.

    Args:
        data: Dictionary containing form_id and form_responses
        org_context: Tuple of (organization_id, is_cross_org)
        thesis_service: Thesis service instance

    Returns:
        List of matching theses
    """
    try:
        # Validate required fields
        if not data.get("form_id"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Form ID is required"
            )
        if not data.get("form_responses"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Form responses are required",
            )

        # Find matching theses
        theses = await thesis_service.find_matching_theses(
            form_id=data["form_id"],
            form_responses=data["form_responses"],
            org_id=org_context[0],
        )

        return theses
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error finding matching theses: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to find matching theses: {str(e)}",
        )


@router.get("/preview/{form_id}", response_model=Dict[str, Any])
async def preview_form_questions(
    form_id: str = Path(..., description="Form ID"),
    thesis_id: Optional[str] = Query(None, description="Optional thesis ID"),
    include_suggestions: bool = Query(
        True, description="Include suggested configurations"
    ),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
) -> Dict[str, Any]:
    """
    Preview form questions with their scoring configuration.

    This endpoint provides a comprehensive view of the form structure with:
    - All sections and questions
    - Existing scoring rules if a thesis_id is provided
    - Suggested configurations for each question type
    - Match rule templates for common scenarios

    Args:
        form_id: Form ID
        thesis_id: Optional thesis ID
        include_suggestions: Whether to include suggested configurations
        org_context: Tuple of (organization_id, is_cross_org)
        thesis_service: Thesis service instance

    Returns:
        Dictionary with form structure and configuration options
    """
    try:
        # If thesis_id is provided, validate thesis ownership
        if thesis_id:
            try:
                await thesis_service.validate_thesis_ownership(
                    thesis_id, org_context[0]
                )
            except ValueError as e:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND
                    if "not found" in str(e)
                    else status.HTTP_403_FORBIDDEN,
                    detail=str(e),
                )

        # Get preview data
        preview = await thesis_service.preview_form_questions(
            form_id, thesis_id, include_suggestions
        )

        return preview
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error previewing form questions: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to preview form questions: {str(e)}",
        )


@router.post("/{thesis_id}/migrate-rules", response_model=Dict[str, Any])
async def migrate_thesis_scoring_rules(
    thesis_id: str = Path(..., description="Thesis ID"),
    org_context: Tuple[str, bool] = Depends(get_org_context),
    thesis_service: ThesisServiceInterface = Depends(get_thesis_service),
) -> Dict[str, Any]:
    """
    Migrate scoring rules for a thesis to the new format.

    This endpoint will:
    1. Convert all existing scoring rules to the new format
    2. Create new rules with the converted data
    3. Return a summary of the migration process

    Args:
        thesis_id: Thesis ID
        org_context: Tuple of (organization_id, is_cross_org)
        thesis_service: Thesis service instance

    Returns:
        Dictionary with migration results
    """
    try:
        # Validate thesis ownership
        try:
            await thesis_service.validate_thesis_ownership(thesis_id, org_context[0])
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND
                if "not found" in str(e)
                else status.HTTP_403_FORBIDDEN,
                detail=str(e),
            )

        # Perform migration
        results = await migrate_thesis_rules(thesis_id, thesis_service)

        # Return appropriate status code based on results
        if not results["success"]:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=results["message"],
            )

        return results

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error migrating thesis rules: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to migrate thesis rules: {str(e)}",
        )
